import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError, throwError } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PasswordChangeService {
  private readonly API_URL = `${environment.apiUrl}/api/Account`;

  constructor(private http: HttpClient) { }

  changePassword(userId: string, currentPassword: string, newPassword: string): Observable<any> {
    return this.http.post<any>(`${this.API_URL}/ChangePassword`, {
      userId,
      currentPassword,
      newPassword
    }).pipe(
      catchError(this.handleError)
    );
  }

  private handleError(error: any) {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = error.error?.message || `Error Code: ${error.status}, Message: ${error.message}`;
    }
    
    return throwError(() => ({ message: errorMessage }));
  }
}
