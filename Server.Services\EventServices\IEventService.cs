﻿using Microsoft.AspNetCore.Http;
using Server.Core.Entities.Events.EventModel;
using Server.Core.Entities.UserManagement.ResponseModel;
using Server.Core.Pagination.PagedResponseModel;
using Server.Core.Pagination.PaginationParameterForEventModel;

namespace Server.Services.EventServices {
    public interface IEventService {
        // CRUD Operations
        Task<ApiResponse<EventResponseDto>> CreateEvent(EventCreateDto dto);
        Task<ApiResponse<EventResponseDto>> UpdateEvent(EventUpdateDto dto);
        Task<ApiResponse<EventResponseDto>> GetEventById(int id);
        Task<ApiResponse<bool>> DeleteEvent(int id);

        // Pagination and Filtering
        Task<PagedResponse<EventResponseDto>> GetAllEvents(PaginationParameterEvent parameters);

        // Approval Operations
        Task<ApiResponse<bool>> ApproveEvent(int id, string reviewerId);
        Task<ApiResponse<bool>> RejectEvent(int id, string reviewerId, string rejectionReason);
        Task<ApiResponse<bool>> SubmitEvent(int id);
        Task<ApiResponse<bool>> CancelEvent(int id);

        // File Operations
        Task<string> UploadFile(IFormFile file, string folderName);

        // Auto-deletion Operations
        Task<ApiResponse<bool>> CheckAndDeleteExpiredEvents();
    }
}
