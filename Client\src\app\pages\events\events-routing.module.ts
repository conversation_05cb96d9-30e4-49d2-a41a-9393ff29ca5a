import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { EventListComponent } from './event-list/event-list.component';
import { EventDetailsComponent } from './event-details/event-details.component';
import { AddEditEventComponent } from './add-edit-event/add-edit-event.component';

const routes: Routes = [
  { path: '', component: EventListComponent },
  { path: 'new', component: AddEditEventComponent },
  { path: ':id', component: EventDetailsComponent },
  { path: ':id/edit', component: AddEditEventComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class EventsRoutingModule {}
