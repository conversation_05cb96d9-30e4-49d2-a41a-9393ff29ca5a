using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Server.Services.EventServices;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Server.BackgroundServices
{
    public class EventCleanupService : BackgroundService
    {
        private readonly ILogger<EventCleanupService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(5); // Check every 5 minutes

        public EventCleanupService(
            ILogger<EventCleanupService> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Event Cleanup Service is starting.");

            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Event Cleanup Service is running cleanup check at: {time}", DateTimeOffset.Now);

                try
                {
                    await CheckAndDeleteExpiredEvents();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "An error occurred while checking for expired events.");
                }

                await Task.Delay(_checkInterval, stoppingToken);
            }

            _logger.LogInformation("Event Cleanup Service is stopping.");
        }

        private async Task CheckAndDeleteExpiredEvents()
        {
            // Create a scope to resolve the event service
            using (var scope = _serviceProvider.CreateScope())
            {
                var eventService = scope.ServiceProvider.GetRequiredService<IEventService>();
                var result = await eventService.CheckAndDeleteExpiredEvents();

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Event cleanup completed: {message}", result.Message);
                }
                else
                {
                    _logger.LogWarning("Event cleanup failed: {message}", result.Message);
                }
            }
        }
    }
}
