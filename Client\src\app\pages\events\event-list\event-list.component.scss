// Global styles for PrimeNG components
:host ::ng-deep {
  // Override for text inputs only
  .p-input-icon-left .p-inputtext,
  input.p-inputtext {
    text-align: left !important;
  }
  .p-select-label {
    padding-top: 5.5px;
    font-size: 0.875rem; // 14px
  }
  .p-inputtext {
    font-size: 0.875rem;
  }

  // Global organizer dropdown panel styling
  .organizer-dropdown .p-dropdown-panel {
    max-height: 250px !important;
    overflow-y: auto !important;
    padding-bottom: 10px !important;

    .p-dropdown-items-wrapper {
      max-height: 220px !important;
      overflow-y: auto !important;
      padding-bottom: 5px !important;
    }

    .p-dropdown-items {
      padding-bottom: 5px !important;
    }

    .p-dropdown-item {
      padding: 8px 12px !important;

      &:last-child {
        margin-bottom: 5px !important;
      }
    }
  }
}

// Main container styles
.event-list-container {
  background-color: #f0f7ff;
  padding: 1.5rem;
  border-radius: 8px;
  max-width: 950px;
  margin: 0 auto;

  h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #1e293b;
  }
}

// Button styles - Standardized to match user list component
.p-button-outlined.p-button-danger {
  border-color: #dc3545;
  color: #dc3545;

  &:hover {
    background-color: rgba(220, 53, 69, 0.04);
  }
}

.create-event-btn {
  background-color: #dc3545;
  border-color: #dc3545;

  &:hover {
    background-color: #c82333;
    border-color: #c82333;
  }
}

// Clear button specific styling to match user list pattern
.clear-btn {
  border-color: #dc3545 !important;
  color: #dc3545 !important;
  background-color: transparent !important;

  &:hover {
    background-color: rgba(220, 53, 69, 0.04) !important;
    border-color: #c82333 !important;
    color: #c82333 !important;
  }

  &:focus {
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
  }
}

// Button styling for consistency with user list
.filter-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 38px;
}

// Tab Styles
.event-tabs {
  max-width: 950px; // Match the container width
  margin: 0 auto;
}

.nav-tabs {
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;

  .nav-link {
    color: #64748b;
    border: none;
    padding: 0.75rem 1rem;
    font-weight: 500;
    font-size: 0.95rem;
    margin-right: 0.5rem;

    &.active {
      color: #e11d48;
      border-bottom: 2px solid #e11d48;
      background-color: transparent;
      font-weight: 600;
    }

    &:hover:not(.active) {
      color: #334155;
      border-bottom: 2px solid #e5e7eb;
    }

    .badge {
      background-color: #e11d48;
      color: white;
      font-size: 0.75rem;
      padding: 0.2rem 0.5rem;
      border-radius: 9999px;
      margin-left: 0.5rem;
    }
  }
}

.filter-btn {
  border-radius: 8px;
  height: 38px;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

// Filter section styles
.filter-section {
  background-color: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  max-width: 950px;
  margin-left: auto;
  margin-right: auto;

  .filter-header {
    h5 {
      font-weight: 600;
      color: #334155;
    }
  }

  // Form labels styling
  .form-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.2rem;
    display: block;
  }

  // Row spacing
  .row {
    margin-bottom: 0;

    &.mb-3 {
      margin-bottom: 0.5rem !important;
    }
  }

  // Column spacing
  .col,
  .col-md-3 {
    padding-left: 0.4rem;
    padding-right: 0.4rem;
    margin-bottom: 0.3rem;
    min-width: 0; // Allow flex shrinking for text truncation
  }

  // Search input styling - matching dropdown and calendar fields
  .p-input-icon-left {
    width: 100%;
    position: relative;

    input {
      width: 100%;
      height: 36px;
      border-radius: 4px;
      padding: 0.3rem 0.5rem 0.3rem 1.8rem;
      font-size: 0.75rem;
      line-height: 1.2;
      color: #374151;
      background-color: #ffffff;
      transition:
        border-color 0.2s ease,
        box-shadow 0.2s ease;

      &::placeholder {
        color: #9ca3af;
        font-size: 0.75rem;
      }

      &:hover {
        border-color: #9ca3af;
      }

      &:focus {
        border-color: #10b981;
        box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
        outline: none;
      }

      &:disabled {
        background-color: #f9fafb;
        color: #6b7280;
        cursor: not-allowed;
      }
    }

    i {
      position: absolute;
      left: 0.5rem;
      top: 50%;
      transform: translateY(-50%);
      color: #6b7280;
      font-size: 0.75rem;
      z-index: 1;
      pointer-events: none;
    }
  }

  // PrimeNG dropdown styling - compact version
  :host ::ng-deep .p-dropdown {
    width: 100%;
    height: 28px;
    border: 1px solid #d1d5db;
    border-radius: 4px;

    &:not(.p-disabled):hover {
      border-color: #9ca3af;
    }

    &:not(.p-disabled).p-focus {
      border-color: #10b981;
      box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
    }

    .p-dropdown-label {
      padding: 0.3rem 0.5rem;
      font-size: 0.75rem;
      color: #374151;
      line-height: 1.2;

      // Text truncation for dropdown labels
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: calc(100% - 1.8rem); // Account for dropdown trigger width
      display: block;
      min-width: 0; // Allow flex shrinking

      &.p-placeholder {
        color: #9ca3af;
      }
    }

    .p-dropdown-trigger {
      width: 1.8rem;
      color: #6b7280;
    }
  }

  // PrimeNG calendar styling - compact version with hidden clear icons
  :host ::ng-deep .p-calendar {
    width: 100%;

    .p-inputtext {
      width: 100%;
      height: 28px;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      padding: 0.3rem 0.5rem;
      font-size: 0.75rem;
      line-height: 1.2;
      color: #374151;
      background-color: #ffffff;
      transition:
        border-color 0.2s ease,
        box-shadow 0.2s ease;

      &::placeholder {
        color: #9ca3af;
        font-size: 0.75rem;
      }

      &:hover {
        border-color: #9ca3af;
      }

      &:focus {
        border-color: #10b981;
        box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
        outline: none;
      }

      &:disabled {
        background-color: #f9fafb;
        color: #6b7280;
        cursor: not-allowed;
      }
    }

    .p-datepicker-trigger {
      color: #6b7280;
      transition: color 0.2s ease;

      &:hover {
        color: #374151;
      }
    }

    // Hide clear icons in calendar fields
    .p-calendar-clear-icon,
    .p-inputtext-clear-icon,
    .p-clear-icon {
      display: none !important;
    }
  }
}

// Compact filter section styles
.compact-filter-section {
  background-color: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  max-width: 950px;
  margin-left: auto;
  margin-right: auto;

  // Filter header row
  .filter-header-row {
    display: flex;
    flex-wrap: nowrap;
    gap: 8px;
    margin-bottom: 10px;

    .filter-header-item {
      display: flex;
      flex-direction: column;
      flex: 1;

      label {
        display: block;
        font-size: 0.7rem;
        color: #64748b;
        margin-bottom: 0.15rem;
        font-weight: 500;
      }

      .filter-input {
        min-width: 120px;
        height: 32px;
      }

      &.date-item {
        .filter-input {
          width: 150px;
        }

        .p-calendar {
          width: 100%;
        }
      }
    }

    @media (max-width: 992px) {
      flex-wrap: wrap;

      .filter-header-item {
        flex: 0 0 calc(50% - 4px);
      }
    }

    @media (max-width: 576px) {
      .filter-header-item {
        flex: 0 0 100%;
      }
    }
  }

  .filter-header {
    margin-bottom: 0.5rem;
    h5 {
      font-weight: 600;
      color: #334155;
      font-size: 0.9rem;
    }
  }
}

// Override any global text-center styles for inputs
:host ::ng-deep {
  .p-inputtext {
    text-align: left !important;
  }
}

// Responsive text truncation adjustments
@media (max-width: 1200px) {
  .filter-section {
    :host ::ng-deep .p-dropdown {
      .p-dropdown-label {
        max-width: calc(
          100% - 1.8rem
        ); // Slightly more space on smaller screens
      }
    }
  }
}

@media (max-width: 768px) {
  .filter-section {
    .col,
    .col-md-3 {
      margin-bottom: 0.4rem;
    }

    // Search input responsive styling
    .p-input-icon-left {
      input {
        height: 26px;
        font-size: 0.7rem;
        padding: 0.25rem 0.4rem 0.25rem 1.6rem;
      }

      i {
        left: 0.4rem;
        font-size: 0.7rem;
      }
    }

    :host ::ng-deep .p-dropdown {
      height: 26px;

      .p-dropdown-label {
        max-width: calc(100% - 1.5rem);
        font-size: 0.7rem;
        padding: 0.25rem 0.4rem;
      }

      .p-dropdown-trigger {
        width: 1.5rem;
      }
    }

    :host ::ng-deep .p-calendar {
      .p-inputtext {
        height: 26px;
        font-size: 0.7rem;
        padding: 0.25rem 0.4rem;
      }
    }
  }
}

// Events Container without scrolling - using standard pagination only
.events-container {
  max-width: 950px;
  margin: 0 auto;
  // Remove fixed height and scrolling - use pagination instead
}

// .event-list-scrollable {
//   // Remove scrolling functionality - use pagination instead
// }

// Horizontal Event Card Styles
.event-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem; // Reduced gap between cards
  max-width: 950px; // Match the Figma design width
  margin: 0 auto;
  margin-bottom: 0;
}

.event-card-horizontal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  width: 100%;
  margin-bottom: 9px;
  min-height: auto; // Remove fixed min-height
  display: flex;

  // Performance optimizations
  will-change: transform; // Optimize for transform changes
  transform: translateZ(0); // Force hardware acceleration
  backface-visibility: hidden; // Prevent flickering

  // Optimized transitions - only on hover, not during scroll
  transition: none; // Remove default transitions

  &:hover {
    transition:
      transform 0.15s ease-out,
      box-shadow 0.15s ease-out; // Faster, smoother transitions
    transform: translateY(-1px) translateZ(0); // Reduced movement, maintain hardware acceleration
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12); // Lighter shadow for better performance
  }

  .event-card-content {
    display: flex;
    width: 100%;
    height: 100%;

    .event-image {
      padding: 10px;
      width: 210px;
      min-width: 144px;
      height: 218px;
      overflow: hidden;
      margin: 0;
      border-radius: 0; // Remove border radius to match Figma

      img {
        width: 100%;
        height: 100%;
        border-radius: 8px; // Remove border radius to match Figma
        object-fit: cover;

        // Performance optimizations for images
        transform: translateZ(0); // Force hardware acceleration
        backface-visibility: hidden; // Prevent flickering
        image-rendering: -webkit-optimize-contrast; // Optimize image rendering
        image-rendering: crisp-edges; // Better performance on some browsers

        &.full-image {
          object-position: center;
          display: block;
        }
      }
    }

    .event-details {
      flex: 1;
      padding: 0.75rem 1rem; // Adjusted padding to match Figma

      .event-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 0.5rem; // Reduced margin
        position: relative;

        .title-category {
          flex: 1;
          margin-right: 1rem;

          .event-title {
            font-size: 18px; // Smaller font size
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 0.25rem 0;
            padding-right: 5rem;
          }

          .event-category {
            display: flex;
            align-items: center;

            .category-text {
              font-size: 0.8rem; // Smaller font size
              color: #0284c7; // Blue color for categories
            }

            .category-separator {
              color: #94a3b8; // Light gray color for separator
              font-size: 0.8rem;
              margin: 0 0.25rem;
            }
          }
        }

        .status-badge {
          padding: 0.15rem 0.5rem; // Smaller padding
          border-radius: 9999px;
          font-size: 0.7rem; // Smaller font size
          font-weight: 500;
          margin-right: 1.4rem;
          white-space: nowrap;
          height: fit-content;
          position: absolute;
          right: 2.5rem;
          top: 0.1rem; // Adjust vertical position slightly
        }
      }

      .event-info-container {
        display: flex;
        flex-wrap: wrap;
        margin-top: 0.5rem;
        gap: 0.5rem; // Reduced gap to match Figma
      }

      .event-info-column {
        flex: 1;
        min-width: 45%; // Ensure columns take reasonable width
      }

      .event-info-row {
        margin-bottom: 0.5rem; // Reduced margin to match Figma

        .info-label {
          color: #64748b;
          font-size: 0.7rem;
          margin-bottom: 0.15rem; // Reduced margin to match Figma
          font-weight: 500;
        }

        .info-value {
          display: flex;
          align-items: center;
          font-size: 0.8rem;
          color: #334155;
          white-space: normal;
          word-break: break-word;
          line-height: 1.3; // Adjusted line height to match Figma

          i {
            color: #64748b;
            font-size: 0.8rem;
            min-width: 16px;
            margin-right: 0.5rem; // Ensure consistent spacing after icons

            &.pi-globe {
              color: #0d6efd; // Blue for online events
            }

            &.pi-map-marker {
              color: #dc3545; // Red for venue events
            }

            &.pi-exclamation-triangle {
              color: #ffc107; // Yellow for missing location
            }
          }
        }
      }
    }
  }
}

.status-badge {
  padding: 0.15rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.7rem;
  font-weight: 500;
  margin-right: 0.5rem;
  white-space: nowrap;
  height: fit-content;

  &.draft {
    background-color: #f5f5f5; // Light gray background
    color: #757575; // Dark gray text
  }

  &.approved {
    background-color: #e8f5e9; // Light green background (matches Figma)
    color: #2e7d32; // Dark green text (matches Figma)
  }

  &.event-started {
    background-color: #e3f2fd; // Light blue background
    color: #1565c0; // Dark blue text
  }

  &.rejected {
    background-color: #ffebee; // Light red background (matches Figma)
    color: #c62828; // Dark red text (matches Figma)
  }

  &.pending {
    background-color: #fff8e1; // Light yellow background
    color: #f59e0b; // Dark yellow text
  }

  &.pending-review {
    background-color: #fff8e1; // Light yellow background
    color: #f59e0b; // Dark yellow text
  }

  &.cancelled {
    background-color: #eceff1; // Light gray background
    color: #546e7a; // Dark gray text
  }
}

// Event header with status badge positioning
.event-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  position: relative;

  .title-category {
    flex: 1;
    margin-right: 1rem;

    .event-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #1e293b;
      margin: 0 0 0.25rem 0;
      padding-right: 5rem;
    }

    .event-category {
      display: flex;
      align-items: center;
      gap: 0.25rem; // Reduced gap between category items to match Figma

      .category-text {
        font-size: 0.8rem;
        color: #0284c7; // Blue color for categories
      }

      .category-separator {
        color: #94a3b8; // Light gray color for separator
        font-size: 0.8rem;
      }
    }
  }

  .status-badge {
    position: absolute;
    right: 3rem; // Increased space to accommodate better button positioning
    top: 0.1rem;
  }

  // Edit button styling - improved positioning and hover effects
  .edit-button {
    position: absolute;
    right: 0.25rem; // Better positioning with small margin from edge
    top: 0.1rem; // Aligned with status badge vertical position
    width: 2rem;
    height: 2rem;
    min-width: 2rem !important; // Ensure consistent button size
    min-height: 2rem !important;
    padding: 0 !important; // Remove default padding
    border-radius: 50% !important;
    color: #ff9f43 !important; // Orange color for icon
    background: transparent !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: background-color 0.2s ease !important;

    &:hover:not(.disabled-edit-button) {
      background-color: rgba(255, 159, 67, 0.1) !important;
      // Ensure hover background stays within button boundaries
      background-clip: padding-box !important;
    }

    &:focus {
      outline: none !important;
      box-shadow: 0 0 0 2px rgba(255, 159, 67, 0.3) !important;
    }

    i {
      font-size: 0.875rem; // Slightly smaller icon for better proportion
      line-height: 1;
    }

    &.disabled-edit-button {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: transparent !important;
      }
    }
  }
}

// Additional styling to ensure proper button behavior and override PrimeNG defaults
::ng-deep {
  .event-list-container .edit-button.p-button {
    // Override PrimeNG button defaults for edit buttons specifically
    padding: 0 !important;
    margin: 0 !important;
    width: 2rem !important;
    height: 2rem !important;
    min-width: 2rem !important;
    min-height: 2rem !important;

    // Ensure the button content is properly centered
    .p-button-label {
      display: none; // Hide any label
    }

    // Ensure icon is properly centered
    .p-button-icon {
      margin: 0 !important;
    }

    // Override any PrimeNG hover states
    &:hover:not(.disabled-edit-button) {
      background-color: rgba(255, 159, 67, 0.1) !important;
      border-color: transparent !important;
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(255, 159, 67, 0.3) !important;
      border-color: transparent !important;
    }

    &:active {
      background-color: rgba(255, 159, 67, 0.2) !important;
      border-color: transparent !important;
    }
  }
}

// Pagination Styles - Using global styles from styles.scss

.app-container {
  overflow: hidden;
  min-height: auto;
}
