import { Injectable } from '@angular/core';
import {
  HttpClient,
  HttpErrorResponse,
  HttpParams,
} from '@angular/common/http';
import { Observable, catchError, map, throwError } from 'rxjs';
import { User } from '../Models/User';
import { AuthService } from './auth.service';
import { UserRequest } from '../Models/user-form.interface';
import { environment } from '../../../environments/environment';
import { PaginationParams, PaginatedResponse } from '../Models/pagination';

@Injectable({
  providedIn: 'root',
})
export class UserDetailsService {
  private readonly API_URL = `${environment.apiUrl}/api/Account`;

  constructor(
    private readonly http: HttpClient,
    private readonly authService: AuthService,
  ) {}

  getUsers(params: PaginationParams): Observable<PaginatedResponse<User>> {
    let httpParams = new HttpParams()
      .set('pageNumber', params.pageNumber.toString())
      .set('pageSize', params.pageSize.toString());

    if (params.sortField) {
      httpParams = httpParams
        .set('sortField', params.sortField)
        .set('sortOrder', params.sortOrder || 'asc');
    }

    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (value) {
          httpParams = httpParams.set(key, value);
        }
      });
    }

    return this.http
      .get<
        PaginatedResponse<User>
      >(`${this.API_URL}/GetUsers`, { params: httpParams })
      .pipe(catchError((err) => this.handleError(err)));
  }

  getUserById(id: string): Observable<User> {
    return this.http
      .get<User>(`${this.API_URL}/GetUserById/${id}`)
      .pipe(catchError((err) => this.handleError(err)));
  }

  addUser(user: UserRequest): Observable<any> {
    return this.http
      .post<any>(`${this.API_URL}/AddUser`, user)
      .pipe(catchError((err) => this.handleError(err)));
  }

  updateUser(id: string, user: UserRequest): Observable<any> {
    return this.http
      .put<any>(`${this.API_URL}/UpdateUser/${id}`, {
        ...user,
      })
      .pipe(catchError((err) => this.handleError(err)));
  }

  toggleUserStatus(userId: string, isActive: boolean): Observable<any> {
    return this.http
      .put<any>(`${this.API_URL}/ToggleStatus/${userId}`, { isActive })
      .pipe(catchError((err) => this.handleError(err)));
  }

  private handleError(error: HttpErrorResponse) {
    if (error.status === 0) {
      return throwError(
        () => new Error('Please check your connection and try again.'),
      );
    }
    return this.authService.handleError(error);
  }

  getUsersByRole(role: string): Observable<User[]> {
    const params = {
      pageNumber: 1,
      pageSize: 100,
      filters: { role: role },
    };

    return this.getUsers(params).pipe(map((response) => response.items));
  }
}
