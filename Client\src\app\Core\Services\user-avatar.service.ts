import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class UserAvatarService {
  private readonly userImages = [
    'assets/images/User1.png',
    'assets/images/User2.png',
    'assets/images/User3.png',
    'assets/images/User4.png',
    'assets/images/User5.png',
    'assets/images/User6.png',
  ];

  private userAvatar = new BehaviorSubject<string>('');

  constructor() {
    if (!this.userAvatar.value) {
      this.setRandomAvatar();
    }
  }

  getUserAvatar(): Observable<string> {
    return this.userAvatar.asObservable();
  }

  getCurrentAvatarValue(): string {
    return this.userAvatar.value;
  }

  setUserAvatar(avatarPath: string): void {
    this.userAvatar.next(avatarPath);
  }

  setRandomAvatar(): void {
    const randomAvatar =
      this.userImages[Math.floor(Math.random() * this.userImages.length)];
    this.userAvatar.next(randomAvatar);
  }
}
