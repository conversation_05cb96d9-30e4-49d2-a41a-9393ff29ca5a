{"ConnectionStrings": {"DefaultConnection": "Server=db;Database=FullStackSFL;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=True;MultipleActiveResultSets=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "JWTSetting": {"securityKey": "ThisIsASuperSecretKey1234567890987654321", "ValidAudience": "http://client", "ValidIssuer": "http://server:5001"}, "AWS": {"Profile": "default", "Region": "eu-north-1", "S3": {"BucketName": "fullstacksfl"}}, "HealthChecks": {"Enabled": true}}