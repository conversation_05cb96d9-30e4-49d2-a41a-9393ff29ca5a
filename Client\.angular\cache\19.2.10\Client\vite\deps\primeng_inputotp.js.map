{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-inputotp.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, forwardRef, EventEmitter, inject, booleanAttribute, ContentChildren, ContentChild, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport { AutoFocus } from 'primeng/autofocus';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { InputText } from 'primeng/inputtext';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"input\"];\nconst _c1 = (a0, a1, a2) => ({\n  $implicit: a0,\n  events: a1,\n  index: a2\n});\nfunction InputOtp_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"input\", 2);\n    i0.ɵɵlistener(\"input\", function InputOtp_ng_container_0_ng_container_1_Template_input_input_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const i_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInput($event, i_r2 - 1));\n    })(\"focus\", function InputOtp_ng_container_0_ng_container_1_Template_input_focus_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onInputFocus($event));\n    })(\"blur\", function InputOtp_ng_container_0_ng_container_1_Template_input_blur_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onInputBlur($event));\n    })(\"paste\", function InputOtp_ng_container_0_ng_container_1_Template_input_paste_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onPaste($event));\n    })(\"keydown\", function InputOtp_ng_container_0_ng_container_1_Template_input_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onKeyDown($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const i_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r2.getModelValue(i_r2))(\"maxLength\", i_r2 === 1 ? ctx_r2.length : 1)(\"type\", ctx_r2.inputType)(\"pSize\", ctx_r2.size)(\"variant\", ctx_r2.variant)(\"readonly\", ctx_r2.readonly)(\"disabled\", ctx_r2.disabled)(\"tabindex\", ctx_r2.tabindex)(\"pAutoFocus\", ctx_r2.getAutofocus(i_r2))(\"ngClass\", ctx_r2.styleClass);\n  }\n}\nfunction InputOtp_ng_container_0_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction InputOtp_ng_container_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputOtp_ng_container_0_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const i_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.inputTemplate || ctx_r2._inputTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(2, _c1, ctx_r2.getToken(i_r2 - 1), ctx_r2.getTemplateEvents(i_r2 - 1), i_r2));\n  }\n}\nfunction InputOtp_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, InputOtp_ng_container_0_ng_container_1_Template, 2, 10, \"ng-container\", 1)(2, InputOtp_ng_container_0_ng_container_2_Template, 2, 6, \"ng-container\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.inputTemplate && !ctx_r2._inputTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.inputTemplate || ctx_r2._inputTemplate);\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-inputotp {\n    display: flex;\n    align-items: center;\n    gap: 0.5rem;\n}\n\n.p-inputotp-input {\n    text-align: center;\n    width: 2.5rem;\n}\n\n.p-inputotp-input.p-inputtext-sm {\n    text-align: center;\n    width: ${dt('inputotp.input.sm.width')};\n}\n\n.p-inputotp-input.p-inputtext-lg {\n    text-align: center;\n    width: ${dt('inputotp.input.lg.width')};\n}\n`;\nconst classes = {\n  root: 'p-inputotp p-component',\n  pcInput: 'p-inputotp-input'\n};\nclass InputOtpStyle extends BaseStyle {\n  name = 'inputotp';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵInputOtpStyle_BaseFactory;\n    return function InputOtpStyle_Factory(__ngFactoryType__) {\n      return (ɵInputOtpStyle_BaseFactory || (ɵInputOtpStyle_BaseFactory = i0.ɵɵgetInheritedFactory(InputOtpStyle)))(__ngFactoryType__ || InputOtpStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InputOtpStyle,\n    factory: InputOtpStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputOtpStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * InputOtp is used to enter one time passwords.\n *\n * [Live Demo](https://www.primeng.org/inputotp/)\n *\n * @module inputotpstyle\n *\n */\nvar InputOtpClasses;\n(function (InputOtpClasses) {\n  /**\n   * Class name of the root element\n   */\n  InputOtpClasses[\"root\"] = \"p-inputotp\";\n  /**\n   * Class name of the input element\n   */\n  InputOtpClasses[\"pcInput\"] = \"p-inputotp-input\";\n})(InputOtpClasses || (InputOtpClasses = {}));\nconst INPUT_OTP_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => InputOtp),\n  multi: true\n};\n/**\n * Input Otp is used to enter one time passwords.\n * @group Components\n */\nclass InputOtp extends BaseComponent {\n  /**\n   * When present, it specifies that the component should have invalid state style.\n   * @group Props\n   */\n  invalid = false;\n  /**\n   * When present, it specifies that the component should be disabled.\n   * @group Props\n   */\n  disabled = false;\n  /**\n   * When present, it specifies that an input field is read-only.\n   * @group Props\n   */\n  readonly = false;\n  /**\n   * Specifies the input variant of the component.\n   * @group Props\n   */\n  variant;\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  tabindex = null;\n  /**\n   * Number of characters to initiate.\n   * @group Props\n   */\n  length = 4;\n  /**\n   * Style class of the input element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Mask pattern.\n   * @group Props\n   */\n  mask = false;\n  /**\n   * When present, it specifies that an input field is integer-only.\n   * @group Props\n   */\n  integerOnly = false;\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @group Props\n   */\n  autofocus;\n  /**\n   * Defines the size of the component.\n   * @group Props\n   */\n  size;\n  /**\n   * Callback to invoke on value change.\n   * @group Emits\n   */\n  onChange = new EventEmitter();\n  /**\n   * Callback to invoke when the component receives focus.\n   * @param {Event} event - Browser event.\n   * @group Emits\n   */\n  onFocus = new EventEmitter();\n  /**\n   * Callback to invoke when the component loses focus.\n   * @param {Event} event - Browser event.\n   * @group Emits\n   */\n  onBlur = new EventEmitter();\n  /**\n   * Input template.\n   * @param {InputOtpInputTemplateContext} context - Context of the template\n   * @see {@link InputOtpInputTemplateContext}\n   * @group Templates\n   */\n  inputTemplate;\n  templates;\n  _inputTemplate;\n  tokens = [];\n  onModelChange = () => {};\n  onModelTouched = () => {};\n  value;\n  get inputMode() {\n    return this.integerOnly ? 'numeric' : 'text';\n  }\n  get inputType() {\n    return this.mask ? 'password' : 'text';\n  }\n  _componentStyle = inject(InputOtpStyle);\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'input':\n          this._inputTemplate = item.template;\n          break;\n        default:\n          this._inputTemplate = item.template;\n          break;\n      }\n    });\n  }\n  getToken(index) {\n    return this.tokens[index];\n  }\n  getTemplateEvents(index) {\n    return {\n      input: event => this.onInput(event, index),\n      keydown: event => this.onKeyDown(event),\n      focus: event => this.onFocus.emit(event),\n      blur: event => this.onBlur.emit(event),\n      paste: event => this.onPaste(event)\n    };\n  }\n  onInput(event, index) {\n    const value = event.target.value;\n    if (index === 0 && value.length > 1) {\n      this.handleOnPaste(value, event);\n      event.stopPropagation();\n      return;\n    }\n    this.tokens[index] = value;\n    this.updateModel(event);\n    if (event.inputType === 'deleteContentBackward') {\n      this.moveToPrev(event);\n    } else if (event.inputType === 'insertText' || event.inputType === 'deleteContentForward') {\n      this.moveToNext(event);\n    }\n  }\n  updateModel(event) {\n    const newValue = this.tokens.join('');\n    this.onModelChange(newValue);\n    this.onChange.emit({\n      originalEvent: event,\n      value: newValue\n    });\n  }\n  writeValue(value) {\n    if (value) {\n      if (Array.isArray(value) && value.length > 0) {\n        this.value = value.slice(0, this.length);\n      } else {\n        this.value = value.toString().split('').slice(0, this.length);\n      }\n    } else {\n      this.value = value;\n    }\n    this.updateTokens();\n    this.cd.markForCheck();\n  }\n  updateTokens() {\n    if (this.value !== null && this.value !== undefined) {\n      if (Array.isArray(this.value)) {\n        this.tokens = [...this.value];\n      } else {\n        this.tokens = this.value.toString().split('');\n      }\n    } else {\n      this.tokens = [];\n    }\n  }\n  getModelValue(i) {\n    return this.tokens[i - 1] || '';\n  }\n  getAutofocus(i) {\n    if (i === 1) {\n      return this.autofocus;\n    }\n    return false;\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  moveToPrev(event) {\n    let prevInput = this.findPrevInput(event.target);\n    if (prevInput) {\n      prevInput.focus();\n      prevInput.select();\n    }\n  }\n  moveToNext(event) {\n    let nextInput = this.findNextInput(event.target);\n    if (nextInput) {\n      nextInput.focus();\n      nextInput.select();\n    }\n  }\n  findNextInput(element) {\n    let nextElement = element.nextElementSibling;\n    if (!nextElement) return;\n    return nextElement.nodeName === 'INPUT' ? nextElement : this.findNextInput(nextElement);\n  }\n  findPrevInput(element) {\n    let prevElement = element.previousElementSibling;\n    if (!prevElement) return;\n    return prevElement.nodeName === 'INPUT' ? prevElement : this.findPrevInput(prevElement);\n  }\n  onInputFocus(event) {\n    event.target.select();\n    this.onFocus.emit(event);\n  }\n  onInputBlur(event) {\n    this.onBlur.emit(event);\n  }\n  onKeyDown(event) {\n    if (event.altKey || event.ctrlKey || event.metaKey) {\n      return;\n    }\n    switch (event.code) {\n      case 'ArrowLeft':\n        this.moveToPrev(event);\n        event.preventDefault();\n        break;\n      case 'ArrowUp':\n      case 'ArrowDown':\n        event.preventDefault();\n        break;\n      case 'Backspace':\n        if (event.target.value.length === 0) {\n          this.moveToPrev(event);\n          event.preventDefault();\n        }\n        break;\n      case 'ArrowRight':\n        this.moveToNext(event);\n        event.preventDefault();\n        break;\n      default:\n        if (this.integerOnly && !(Number(event.key) >= 0 && Number(event.key) <= 9) || this.tokens.join('').length >= this.length && event.code !== 'Delete') {\n          event.preventDefault();\n        }\n        break;\n    }\n  }\n  onPaste(event) {\n    if (!this.disabled && !this.readonly) {\n      let paste = event.clipboardData.getData('text');\n      if (paste.length) {\n        this.handleOnPaste(paste, event);\n      }\n      event.preventDefault();\n    }\n  }\n  handleOnPaste(paste, event) {\n    let pastedCode = paste.substring(0, this.length + 1);\n    if (!this.integerOnly || !isNaN(pastedCode)) {\n      this.tokens = pastedCode.split('');\n      this.updateModel(event);\n    }\n  }\n  getRange(n) {\n    return Array.from({\n      length: n\n    }, (_, index) => index + 1);\n  }\n  trackByFn(index) {\n    return index;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵInputOtp_BaseFactory;\n    return function InputOtp_Factory(__ngFactoryType__) {\n      return (ɵInputOtp_BaseFactory || (ɵInputOtp_BaseFactory = i0.ɵɵgetInheritedFactory(InputOtp)))(__ngFactoryType__ || InputOtp);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: InputOtp,\n    selectors: [[\"p-inputOtp\"], [\"p-inputotp\"], [\"p-input-otp\"]],\n    contentQueries: function InputOtp_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c0, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostAttrs: [1, \"p-inputotp\", \"p-component\"],\n    inputs: {\n      invalid: \"invalid\",\n      disabled: \"disabled\",\n      readonly: \"readonly\",\n      variant: \"variant\",\n      tabindex: \"tabindex\",\n      length: \"length\",\n      styleClass: \"styleClass\",\n      mask: \"mask\",\n      integerOnly: \"integerOnly\",\n      autofocus: [2, \"autofocus\", \"autofocus\", booleanAttribute],\n      size: \"size\"\n    },\n    outputs: {\n      onChange: \"onChange\",\n      onFocus: \"onFocus\",\n      onBlur: \"onBlur\"\n    },\n    features: [i0.ɵɵProvidersFeature([INPUT_OTP_VALUE_ACCESSOR, InputOtpStyle]), i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 2,\n    consts: [[4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [4, \"ngIf\"], [\"type\", \"text\", \"pInputText\", \"\", 1, \"p-inputotp-input\", 3, \"input\", \"focus\", \"blur\", \"paste\", \"keydown\", \"value\", \"maxLength\", \"type\", \"pSize\", \"variant\", \"readonly\", \"disabled\", \"tabindex\", \"pAutoFocus\", \"ngClass\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n    template: function InputOtp_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, InputOtp_ng_container_0_Template, 3, 2, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngForOf\", ctx.getRange(ctx.length))(\"ngForTrackBy\", ctx.trackByFn);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, InputText, AutoFocus, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputOtp, [{\n    type: Component,\n    args: [{\n      selector: 'p-inputOtp, p-inputotp, p-input-otp',\n      standalone: true,\n      imports: [CommonModule, InputText, AutoFocus, SharedModule],\n      template: `\n        <ng-container *ngFor=\"let i of getRange(length); trackBy: trackByFn\">\n            <ng-container *ngIf=\"!inputTemplate && !_inputTemplate\">\n                <input\n                    type=\"text\"\n                    pInputText\n                    [value]=\"getModelValue(i)\"\n                    [maxLength]=\"i === 1 ? length : 1\"\n                    [type]=\"inputType\"\n                    class=\"p-inputotp-input\"\n                    [pSize]=\"size\"\n                    [variant]=\"variant\"\n                    [readonly]=\"readonly\"\n                    [disabled]=\"disabled\"\n                    [tabindex]=\"tabindex\"\n                    (input)=\"onInput($event, i - 1)\"\n                    (focus)=\"onInputFocus($event)\"\n                    (blur)=\"onInputBlur($event)\"\n                    (paste)=\"onPaste($event)\"\n                    (keydown)=\"onKeyDown($event)\"\n                    [pAutoFocus]=\"getAutofocus(i)\"\n                    [ngClass]=\"styleClass\"\n                />\n            </ng-container>\n            <ng-container *ngIf=\"inputTemplate || _inputTemplate\">\n                <ng-container *ngTemplateOutlet=\"inputTemplate || _inputTemplate; context: { $implicit: getToken(i - 1), events: getTemplateEvents(i - 1), index: i }\"> </ng-container>\n            </ng-container>\n        </ng-container>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [INPUT_OTP_VALUE_ACCESSOR, InputOtpStyle],\n      host: {\n        class: 'p-inputotp p-component'\n      }\n    }]\n  }], null, {\n    invalid: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    variant: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    length: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    mask: [{\n      type: Input\n    }],\n    integerOnly: [{\n      type: Input\n    }],\n    autofocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    size: [{\n      type: Input\n    }],\n    onChange: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    inputTemplate: [{\n      type: ContentChild,\n      args: ['input', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass InputOtpModule {\n  static ɵfac = function InputOtpModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InputOtpModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputOtpModule,\n    imports: [InputOtp, SharedModule],\n    exports: [InputOtp, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [InputOtp, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputOtpModule, [{\n    type: NgModule,\n    args: [{\n      imports: [InputOtp, SharedModule],\n      exports: [InputOtp, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INPUT_OTP_VALUE_ACCESSOR, InputOtp, InputOtpClasses, InputOtpModule, InputOtpStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC3B,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AACT;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,WAAW,SAAS,SAAS,uEAAuE,QAAQ;AAC7G,MAAG,cAAc,GAAG;AACpB,YAAM,OAAU,cAAc,EAAE;AAChC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,QAAQ,OAAO,CAAC,CAAC;AAAA,IACxD,CAAC,EAAE,SAAS,SAAS,uEAAuE,QAAQ;AAClG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,MAAM,CAAC;AAAA,IACnD,CAAC,EAAE,QAAQ,SAAS,sEAAsE,QAAQ;AAChG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,SAAS,SAAS,uEAAuE,QAAQ;AAClG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,QAAQ,MAAM,CAAC;AAAA,IAC9C,CAAC,EAAE,WAAW,SAAS,yEAAyE,QAAQ;AACtG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,MAAM,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,cAAc,EAAE;AAChC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,OAAO,cAAc,IAAI,CAAC,EAAE,aAAa,SAAS,IAAI,OAAO,SAAS,CAAC,EAAE,QAAQ,OAAO,SAAS,EAAE,SAAS,OAAO,IAAI,EAAE,WAAW,OAAO,OAAO,EAAE,YAAY,OAAO,QAAQ,EAAE,YAAY,OAAO,QAAQ,EAAE,YAAY,OAAO,QAAQ,EAAE,cAAc,OAAO,aAAa,IAAI,CAAC,EAAE,WAAW,OAAO,UAAU;AAAA,EACzU;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,gEAAgE,GAAG,GAAG,gBAAgB,CAAC;AACxG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,cAAc,EAAE;AAChC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,iBAAiB,OAAO,cAAc,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,SAAS,OAAO,CAAC,GAAG,OAAO,kBAAkB,OAAO,CAAC,GAAG,IAAI,CAAC;AAAA,EAC7M;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,iDAAiD,GAAG,IAAI,gBAAgB,CAAC,EAAE,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC;AACvK,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,iBAAiB,CAAC,OAAO,cAAc;AACrE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,iBAAiB,OAAO,cAAc;AAAA,EACrE;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAcO,GAAG,yBAAyB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,aAK7B,GAAG,yBAAyB,CAAC;AAAA;AAAA;AAG1C,IAAM,UAAU;AAAA,EACd,MAAM;AAAA,EACN,SAAS;AACX;AACA,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,kBAAiB;AAI1B,EAAAA,iBAAgB,MAAM,IAAI;AAI1B,EAAAA,iBAAgB,SAAS,IAAI;AAC/B,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAM,2BAA2B;AAAA,EAC/B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,QAAQ;AAAA,EACtC,OAAO;AACT;AAKA,IAAM,WAAN,MAAM,kBAAiB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5B,UAAU,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3B,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO1B;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,CAAC;AAAA,EACV,gBAAgB,MAAM;AAAA,EAAC;AAAA,EACvB,iBAAiB,MAAM;AAAA,EAAC;AAAA,EACxB;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,cAAc,YAAY;AAAA,EACxC;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,OAAO,aAAa;AAAA,EAClC;AAAA,EACA,kBAAkB,OAAO,aAAa;AAAA,EACtC,qBAAqB;AACnB,SAAK,UAAU,QAAQ,UAAQ;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,iBAAiB,KAAK;AAC3B;AAAA,QACF;AACE,eAAK,iBAAiB,KAAK;AAC3B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,SAAS,OAAO;AACd,WAAO,KAAK,OAAO,KAAK;AAAA,EAC1B;AAAA,EACA,kBAAkB,OAAO;AACvB,WAAO;AAAA,MACL,OAAO,WAAS,KAAK,QAAQ,OAAO,KAAK;AAAA,MACzC,SAAS,WAAS,KAAK,UAAU,KAAK;AAAA,MACtC,OAAO,WAAS,KAAK,QAAQ,KAAK,KAAK;AAAA,MACvC,MAAM,WAAS,KAAK,OAAO,KAAK,KAAK;AAAA,MACrC,OAAO,WAAS,KAAK,QAAQ,KAAK;AAAA,IACpC;AAAA,EACF;AAAA,EACA,QAAQ,OAAO,OAAO;AACpB,UAAM,QAAQ,MAAM,OAAO;AAC3B,QAAI,UAAU,KAAK,MAAM,SAAS,GAAG;AACnC,WAAK,cAAc,OAAO,KAAK;AAC/B,YAAM,gBAAgB;AACtB;AAAA,IACF;AACA,SAAK,OAAO,KAAK,IAAI;AACrB,SAAK,YAAY,KAAK;AACtB,QAAI,MAAM,cAAc,yBAAyB;AAC/C,WAAK,WAAW,KAAK;AAAA,IACvB,WAAW,MAAM,cAAc,gBAAgB,MAAM,cAAc,wBAAwB;AACzF,WAAK,WAAW,KAAK;AAAA,IACvB;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,UAAM,WAAW,KAAK,OAAO,KAAK,EAAE;AACpC,SAAK,cAAc,QAAQ;AAC3B,SAAK,SAAS,KAAK;AAAA,MACjB,eAAe;AAAA,MACf,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,OAAO;AACT,UAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS,GAAG;AAC5C,aAAK,QAAQ,MAAM,MAAM,GAAG,KAAK,MAAM;AAAA,MACzC,OAAO;AACL,aAAK,QAAQ,MAAM,SAAS,EAAE,MAAM,EAAE,EAAE,MAAM,GAAG,KAAK,MAAM;AAAA,MAC9D;AAAA,IACF,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AACA,SAAK,aAAa;AAClB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,eAAe;AACb,QAAI,KAAK,UAAU,QAAQ,KAAK,UAAU,QAAW;AACnD,UAAI,MAAM,QAAQ,KAAK,KAAK,GAAG;AAC7B,aAAK,SAAS,CAAC,GAAG,KAAK,KAAK;AAAA,MAC9B,OAAO;AACL,aAAK,SAAS,KAAK,MAAM,SAAS,EAAE,MAAM,EAAE;AAAA,MAC9C;AAAA,IACF,OAAO;AACL,WAAK,SAAS,CAAC;AAAA,IACjB;AAAA,EACF;AAAA,EACA,cAAc,GAAG;AACf,WAAO,KAAK,OAAO,IAAI,CAAC,KAAK;AAAA,EAC/B;AAAA,EACA,aAAa,GAAG;AACd,QAAI,MAAM,GAAG;AACX,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,YAAY,KAAK,cAAc,MAAM,MAAM;AAC/C,QAAI,WAAW;AACb,gBAAU,MAAM;AAChB,gBAAU,OAAO;AAAA,IACnB;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,YAAY,KAAK,cAAc,MAAM,MAAM;AAC/C,QAAI,WAAW;AACb,gBAAU,MAAM;AAChB,gBAAU,OAAO;AAAA,IACnB;AAAA,EACF;AAAA,EACA,cAAc,SAAS;AACrB,QAAI,cAAc,QAAQ;AAC1B,QAAI,CAAC,YAAa;AAClB,WAAO,YAAY,aAAa,UAAU,cAAc,KAAK,cAAc,WAAW;AAAA,EACxF;AAAA,EACA,cAAc,SAAS;AACrB,QAAI,cAAc,QAAQ;AAC1B,QAAI,CAAC,YAAa;AAClB,WAAO,YAAY,aAAa,UAAU,cAAc,KAAK,cAAc,WAAW;AAAA,EACxF;AAAA,EACA,aAAa,OAAO;AAClB,UAAM,OAAO,OAAO;AACpB,SAAK,QAAQ,KAAK,KAAK;AAAA,EACzB;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA,EACA,UAAU,OAAO;AACf,QAAI,MAAM,UAAU,MAAM,WAAW,MAAM,SAAS;AAClD;AAAA,IACF;AACA,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,aAAK,WAAW,KAAK;AACrB,cAAM,eAAe;AACrB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,cAAM,eAAe;AACrB;AAAA,MACF,KAAK;AACH,YAAI,MAAM,OAAO,MAAM,WAAW,GAAG;AACnC,eAAK,WAAW,KAAK;AACrB,gBAAM,eAAe;AAAA,QACvB;AACA;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB,cAAM,eAAe;AACrB;AAAA,MACF;AACE,YAAI,KAAK,eAAe,EAAE,OAAO,MAAM,GAAG,KAAK,KAAK,OAAO,MAAM,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK,EAAE,EAAE,UAAU,KAAK,UAAU,MAAM,SAAS,UAAU;AACpJ,gBAAM,eAAe;AAAA,QACvB;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,QAAQ,OAAO;AACb,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,UAAU;AACpC,UAAI,QAAQ,MAAM,cAAc,QAAQ,MAAM;AAC9C,UAAI,MAAM,QAAQ;AAChB,aAAK,cAAc,OAAO,KAAK;AAAA,MACjC;AACA,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,cAAc,OAAO,OAAO;AAC1B,QAAI,aAAa,MAAM,UAAU,GAAG,KAAK,SAAS,CAAC;AACnD,QAAI,CAAC,KAAK,eAAe,CAAC,MAAM,UAAU,GAAG;AAC3C,WAAK,SAAS,WAAW,MAAM,EAAE;AACjC,WAAK,YAAY,KAAK;AAAA,IACxB;AAAA,EACF;AAAA,EACA,SAAS,GAAG;AACV,WAAO,MAAM,KAAK;AAAA,MAChB,QAAQ;AAAA,IACV,GAAG,CAAC,GAAG,UAAU,QAAQ,CAAC;AAAA,EAC5B;AAAA,EACA,UAAU,OAAO;AACf,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,iBAAiB,mBAAmB;AAClD,cAAQ,0BAA0B,wBAA2B,sBAAsB,SAAQ,IAAI,qBAAqB,SAAQ;AAAA,IAC9H;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,GAAG,CAAC,YAAY,GAAG,CAAC,aAAa,CAAC;AAAA,IAC3D,gBAAgB,SAAS,wBAAwB,IAAI,KAAK,UAAU;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,cAAc,aAAa;AAAA,IAC1C,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,aAAa;AAAA,MACb,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,MACzD,MAAM;AAAA,IACR;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,0BAA0B,aAAa,CAAC,GAAM,0BAA0B;AAAA,IAC1G,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,QAAQ,QAAQ,cAAc,IAAI,GAAG,oBAAoB,GAAG,SAAS,SAAS,QAAQ,SAAS,WAAW,SAAS,aAAa,QAAQ,SAAS,WAAW,YAAY,YAAY,YAAY,cAAc,SAAS,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,CAAC;AAAA,IAC5U,UAAU,SAAS,kBAAkB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,kCAAkC,GAAG,GAAG,gBAAgB,CAAC;AAAA,MAC5E;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,IAAI,SAAS,IAAI,MAAM,CAAC,EAAE,gBAAgB,IAAI,SAAS;AAAA,MAClF;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,SAAY,MAAS,kBAAkB,WAAW,WAAW,YAAY;AAAA,IACrH,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,WAAW,WAAW,YAAY;AAAA,MAC1D,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA6BV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,0BAA0B,aAAa;AAAA,MACnD,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,UAAU,YAAY;AAAA,IAChC,SAAS,CAAC,UAAU,YAAY;AAAA,EAClC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,UAAU,cAAc,YAAY;AAAA,EAChD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU,YAAY;AAAA,MAChC,SAAS,CAAC,UAAU,YAAY;AAAA,IAClC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["InputOtpClasses"]}