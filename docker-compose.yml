version: "3.8"

services:
  # Frontend Angular application
  frontend:
    container_name: fullstack-frontend
    build:
      context: ./Client
      dockerfile: Dockerfile
    ports:
      - "4200:80"
    depends_on:
      - server
    networks:
      - fullstack-network
    restart: unless-stopped

  # Backend .NET API
  server:
    container_name: fullstack-server
    build:
      context: ./
      dockerfile: Server/Dockerfile
    ports:
      - "5020:5001"
      - "5022:5001"
    depends_on:
      - db
    environment:
      - ASPNETCORE_ENVIRONMENT=Docker
      - ASPNETCORE_URLS=http://+:5001
      - ConnectionStrings__DefaultConnection=Server=db;Database=FullStackSFL;User Id=sa;Password=YourStrong!Passw0rd;TrustServerCertificate=True;MultipleActiveResultSets=true
      - AWS__Region=eu-north-1
      - AWS__S3__BucketName=fullstacksfl
      - AWS__S3__AccessKey=${AWS_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_SECRET_KEY}
      - JWTSetting__ValidAudience=http://client
      - JWTSetting__ValidIssuer=http://server:5001
    volumes:
      - server-data:/app/wwwroot
    networks:
      - fullstack-network
    restart: unless-stopped

  # SQL Server database
  db:
    container_name: fullstack-db
    image: mcr.microsoft.com/mssql/server:2022-latest
    environment:
      - ACCEPT_EULA=Y
      - MSSQL_SA_PASSWORD=YourStrong!Passw0rd
    ports:
      - "1433:1433"
    volumes:
      - sqlserver-data:/var/opt/mssql
    networks:
      - fullstack-network
    restart: unless-stopped

networks:
  fullstack-network:
    driver: bridge

volumes:
  sqlserver-data:
  server-data:
