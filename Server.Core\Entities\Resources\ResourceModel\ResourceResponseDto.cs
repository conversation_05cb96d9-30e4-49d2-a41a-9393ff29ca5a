﻿using Server.Core.Entities.Resources.ContactDetailsModel;
using Server.Core.Entities.Resources.SocialMediaModel;

namespace Server.Core.Entities.Resources.ResourceModel {
    public class ResourceResponseDto {

        public int Id { get; set; }
        public string OrganizationTitle { get; set; }
        public string SubTitle { get; set; }
        public string ResourceCategory { get; set; }
        public string ResourceImageUrl { get; set; }
        public string ResourceLogoUrl { get; set; }

        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string ZipCode { get; set; }

        public string ShortDescription { get; set; }
        public string LongDescription { get; set; }

        public string ResourceTypeName { get; set; }

        public List<string> Services { get; set; }
        public ContactDetailsResponseDto ContactDetails { get; set; }
        public SocialMediaResponseDto SocialMedia { get; set; }

        // Additional properties that might be useful for the client
        public string FullAddress => $"{Address}, {City}, {State} {ZipCode}";
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
