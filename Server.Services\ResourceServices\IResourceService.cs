﻿using Microsoft.AspNetCore.Http;
using Server.Core.Entities.Resources.ResourceModel;
using Server.Core.Entities.UserManagement.ResponseModel;
using Server.Core.Pagination.PagedResponseModel;
using Server.Core.Pagination.PaginationParameterForResourceModel;

namespace Server.Services.ResourceServices {
    public interface IResourceService {

        Task<PagedResponse<ResourceResponseDto>> GetAllResource(PaginationParameterEvents resource);
        Task<ResourceResponseDto> GetResourceById(int id);

        Task<Response> CreateResource(ResourceCreateDTO resource);
        Task<Response> UpdateResource(ResourceUpdateDto resource);
        Task<Response> DeleteResource(int id);
        Task<string> UploadFile(IFormFile file, string folderName);
        void DeleteFile(string filePath);
    }
}

