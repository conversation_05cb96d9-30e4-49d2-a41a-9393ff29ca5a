import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import {
  HttpClient,
  HttpErrorResponse,
  HttpParams,
} from '@angular/common/http';
import { AuthService } from './auth.service';
import { catchError, Observable, throwError } from 'rxjs';
import { Resource, ApiResponse } from '../Models/resources';
import { PaginatedResponse, PaginationParams } from '../Models/pagination';

@Injectable({
  providedIn: 'root',
})
export class ResourcesService {
  private readonly API_URL = `${environment.apiUrl}/api/Resources`;

  constructor(
    private readonly http: HttpClient,
    private readonly authService: AuthService,
  ) {}

  getResources(
    params: PaginationParams,
  ): Observable<PaginatedResponse<Resource>> {
    let httpParams = new HttpParams()
      .set('pageNumber', params.pageNumber.toString())
      .set('pageSize', params.pageSize.toString());

    if (params.sortField) {
      httpParams = httpParams
        .set('sortField', params.sortField)
        .set('sortOrder', params.sortOrder || 'asc');
    }

    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (value) {
          httpParams = httpParams.set(key, value);
        }
      });
    }

    return this.http
      .get<
        PaginatedResponse<Resource>
      >(`${this.API_URL}/GetResources`, { params: httpParams })
      .pipe(catchError((err) => this.handleError(err)));
  }

  AddResource(resource: FormData): Observable<ApiResponse<Resource>> {
    return this.http
      .post<ApiResponse<Resource>>(`${this.API_URL}/AddResources`, resource)
      .pipe(catchError((err) => this.handleError(err)));
  }

  UpdateResource(resource: FormData): Observable<ApiResponse<Resource>> {
    return this.http
      .put<ApiResponse<Resource>>(`${this.API_URL}/UpdateResources`, resource)
      .pipe(catchError((err) => this.handleError(err)));
  }

  DeleteResource(id: number): Observable<ApiResponse<Resource>> {
    return this.http
      .delete<ApiResponse<Resource>>(`${this.API_URL}/DeleteResources/${id}`)
      .pipe(catchError((err) => this.handleError(err)));
  }

  GetResourceById(id: number): Observable<ApiResponse<Resource>> {
    return this.http
      .get<ApiResponse<Resource>>(`${this.API_URL}/GetResources/${id}`)
      .pipe(catchError((err) => this.handleError(err)));
  }

  private handleError(error: HttpErrorResponse) {
    console.log('ResourcesService error handler:', error);

    // Check for duplicate title error
    if (error.status === 400) {
      // Check if the error response contains the duplicate title message
      const duplicateTitleMessage = 'Resource with this title already exists';

      // Check in various places where the message might be
      if (
        (typeof error.error === 'string' &&
          error.error.includes(duplicateTitleMessage)) ||
        (error.error &&
          typeof error.error === 'object' &&
          error.error.message === duplicateTitleMessage) ||
        (error.error &&
          typeof error.error === 'object' &&
          error.error.Message === duplicateTitleMessage)
      ) {
        console.log('Duplicate title error detected in service');
        return throwError(() => new Error(duplicateTitleMessage));
      }
    }

    if (error.status === 401) {
      return throwError(() => new Error('Unauthorized'));
    }

    return this.authService.handleError(error);
  }
}
