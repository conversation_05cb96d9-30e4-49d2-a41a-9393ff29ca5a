﻿using AutoMapper;
using Server.Core.Entities.Resources.ContactDetailsModel;
using Server.Core.Entities.Resources.ResourceModel;
using Server.Core.Entities.Resources.SocialMediaModel;

namespace Server.Core.MappingProfile {
    public class ResourceMappingProfile : Profile {

        public ResourceMappingProfile() {

            // Resource mapping
            CreateMap<ResourceCreateDTO, Resource>()
                .ForMember(dest => dest.ResourceImagePath, opt => opt.MapFrom(src => src.ResourceImagePath))
                .ForMember(dest => dest.ResourceLogoPath, opt => opt.MapFrom(src => src.ResourceLogoPath))
                .ForMember(dest => dest.Services, opt => opt.Ignore())
                .ForMember(dest => dest.ContactDetails, opt => opt.Ignore())
                .ForMember(dest => dest.SocialMedia, opt => opt.Ignore());

            CreateMap<ResourceUpdateDto, Resource>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.Services, opt => opt.Ignore())
                .ForMember(dest => dest.ContactDetails, opt => opt.Ignore())
                .ForMember(dest => dest.SocialMedia, opt => opt.Ignore());

            CreateMap<Resource, ResourceResponseDto>()
                .ForMember(dest => dest.ResourceImageUrl, opt => opt.MapFrom(src => GetFileUrl(src.ResourceImagePath)))
                .ForMember(dest => dest.ResourceLogoUrl, opt => opt.MapFrom(src => GetFileUrl(src.ResourceLogoPath)))
                .ForMember(dest => dest.ResourceTypeName, opt => opt.MapFrom(src => src.Type.ToString()))
                .ForMember(dest => dest.Services, opt => opt.MapFrom(src => src.Services.Select(s => s.ServiceName).ToList()));

            // Contact details mapping
            CreateMap<ContactDetailsDto, ContactDetails>();
            CreateMap<ContactDetails, ContactDetailsResponseDto>();

            // Social media mapping
            CreateMap<SocialMediaDto, SocialMedia>();
            CreateMap<SocialMedia, SocialMediaResponseDto>();

        }
        private string GetFileUrl(string filePath) {
            if (string.IsNullOrEmpty(filePath)) {
                return null;
            }

            // Convert relative path to URL - ensure no double slashes
            return filePath;
        }
    }
}
