import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { AuthService } from '../../Core/Services/auth.service';
import { Router, NavigationEnd } from '@angular/router';
import { UserDetailsService } from '../../Core/Services/UserDetails.service';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { MessagesModule } from 'primeng/messages';
import { MessageModule } from 'primeng/message';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { TextareaModule } from 'primeng/textarea';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { Subscription, filter } from 'rxjs';
import { UserAvatarService } from '../../Core/Services/user-avatar.service';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ToastModule,
    MessagesModule,
    MessageModule,
    InputTextModule,
    ButtonModule,
    DropdownModule,
    TextareaModule,
    ProgressSpinnerModule,
  ],
  providers: [MessageService],
})
export class ProfileComponent implements OnInit, OnDestroy {
  userAvatar: string = '';
  userId: string = '';
  userName: string = '';
  userEmail: string = '';
  userRole: string = '';
  isActive: boolean = true;
  website: string = '';
  phoneNumber: string = '';
  description: string = '';
  facebookId: string = '';
  twitterId: string = '';
  isLoading: boolean = false;
  errorMessages: any[] = [];

  // Form-related properties
  profileForm!: FormGroup;
  isEditMode: boolean = false;
  isSubmitting: boolean = false;
  validationErrors: string[] = [];
  formSubmitted: boolean = false;

  private routerSubscription: Subscription | null = null;
  private avatarSubscription: Subscription | null = null;

  constructor(
    private authService: AuthService,
    private userService: UserDetailsService,
    private router: Router,
    private messageService: MessageService,
    private userAvatarService: UserAvatarService,
    private fb: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadUserProfile();

    this.avatarSubscription = this.userAvatarService
      .getUserAvatar()
      .subscribe((avatar) => {
        this.userAvatar = avatar;
      });

    this.routerSubscription = this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        if (this.router.url === '/profile') {
          this.loadUserProfile();
        }
      });
  }

  ngOnDestroy(): void {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }

    if (this.avatarSubscription) {
      this.avatarSubscription.unsubscribe();
    }
  }

  loadUserProfile(): void {
    this.authService.debugToken();

    const refreshed = this.authService.refreshUserInfoFromToken();

    const userInfo = this.authService.getUserInfo();

    const token = this.authService.getToken();

    if (userInfo) {
      this.userId = userInfo.id || userInfo.nameid || userInfo.sub || '';

      if (!this.userId && userInfo.name) {
        this.userName = userInfo.name;
        this.userEmail = userInfo.email || '';
      }

      if (this.userId) {
        this.isLoading = true;
        this.userService.getUserById(this.userId).subscribe({
          next: (user) => {
            this.userName = user.fullName || '';
            this.userEmail = user.email || '';
            this.userRole =
              user.roles && user.roles.length > 0 ? user.roles[0] : '';
            this.isActive = user.isActive;
            this.phoneNumber = user.phoneNumber || '';
            this.website = user.website || '';
            this.description = user.description || '';
            this.facebookId = user.facebook || '';
            this.twitterId = user.twitter || '';
            this.isLoading = false;

            this.errorMessages = [];
            this.populateForm();
          },
          error: (error) => {
            console.error('Error loading user profile:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to load profile data',
            });

            this.errorMessages = [
              {
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to load profile data from server',
              },
            ];

            this.isLoading = false;

            if (userInfo) {
              this.userName = userInfo.name || '';
              this.userEmail = userInfo.email || '';
              this.userRole = userInfo.role;
              this.isActive = userInfo.isActive;
              this.phoneNumber = userInfo.phoneNumber || '';
              this.website = userInfo.website || '';
              this.description = userInfo.description || '';
              this.facebookId = userInfo.facebook || '';
              this.twitterId = userInfo.twitter || '';
            }
          },
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'User information not found',
        });

        this.errorMessages = [
          {
            severity: 'error',
            summary: 'Error',
            detail: 'User information not found. Please try logging in again.',
          },
        ];
      }
    } else {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'User information not found',
      });

      this.errorMessages = [
        {
          severity: 'error',
          summary: 'Error',
          detail: 'User information not found. Please try logging in again.',
        },
      ];
    }
  }

  initializeForm(): void {
    this.profileForm = this.fb.group({
      fullName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      role: ['', [Validators.required]],
      phoneNumber: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{10}$')],
      ],
      website: [
        '',
        [
          Validators.pattern(
            '(https?:\\/\\/)?(www\\.)?[a-zA-Z0-9-]+(\\.[a-zA-Z]{2,})+\\/?.*',
          ),
        ],
      ],
      description: [''],
      facebook: [
        '',
        [
          Validators.pattern(
            '(https?:\\/\\/)?(www\\.)?facebook\\.com\\/[a-zA-Z0-9\\.]+',
          ),
        ],
      ],
      twitter: [
        '',
        [
          Validators.pattern(
            '(https?:\\/\\/)?(www\\.)?(twitter\\.com|x\\.com)\\/[A-Za-z0-9_]{1,15}',
          ),
        ],
      ],
    });

    // Disable email field as it shouldn't be editable
    this.profileForm.get('email')?.disable();
  }

  populateForm(): void {
    if (this.profileForm) {
      this.profileForm.patchValue({
        fullName: this.userName,
        email: this.userEmail,
        role: this.userRole,
        phoneNumber: this.phoneNumber,
        website: this.website,
        description: this.description,
        facebook: this.facebookId,
        twitter: this.twitterId,
      });
    }
  }

  toggleEditMode(): void {
    this.isEditMode = !this.isEditMode;
    this.validationErrors = [];
    this.formSubmitted = false;

    if (!this.isEditMode) {
      // Reset form to original values when canceling edit
      this.populateForm();
    }
  }

  cancelEdit(): void {
    this.isEditMode = false;
    this.validationErrors = [];
    this.formSubmitted = false;
    this.populateForm();
  }

  onSubmit(): void {
    this.formSubmitted = true;
    this.validationErrors = [];

    if (this.profileForm.invalid) {
      this.collectValidationErrors();
      return;
    }

    this.isSubmitting = true;
    const formValue = this.profileForm.value;

    // Prepare the update payload (excluding role for self-edit)
    const updatePayload = {
      FullName: formValue.fullName,
      Email: this.userEmail, // Keep original email
      Password: '', // Not updating password in profile edit
      PhoneNumber: formValue.phoneNumber,
      Website: formValue.website || '',
      Description: formValue.description || '',
      Facebook: formValue.facebook || '',
      Twitter: formValue.twitter || '',
      Roles: [this.userRole], // Keep original role
      IsActive: this.isActive,
      CreatedByName: '',
    };

    this.userService.updateUser(this.userId, updatePayload).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Profile updated successfully',
          });
          this.isEditMode = false;
          this.loadUserProfile(); // Reload to get updated data
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: response.message || 'Failed to update profile',
          });
        }
        this.isSubmitting = false;
      },
      error: (err: HttpErrorResponse) => {
        console.error('Error updating profile:', err);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update profile. Please try again.',
        });
        this.isSubmitting = false;
      },
    });
  }

  private collectValidationErrors(): void {
    this.validationErrors = [];
    const controls = this.profileForm.controls;

    if (controls['fullName'].errors) {
      this.validationErrors.push('Name is required');
    }

    if (controls['phoneNumber'].errors) {
      if (controls['phoneNumber'].errors['required']) {
        this.validationErrors.push('Phone Number is required');
      } else if (controls['phoneNumber'].errors['pattern']) {
        this.validationErrors.push('Phone Number must be 10 digits');
      }
    }

    if (controls['website'].errors && controls['website'].errors['pattern']) {
      this.validationErrors.push('Please enter a valid Website URL');
    }

    if (controls['facebook'].errors && controls['facebook'].errors['pattern']) {
      this.validationErrors.push('Please enter a valid Facebook URL');
    }

    if (controls['twitter'].errors && controls['twitter'].errors['pattern']) {
      this.validationErrors.push('Please enter a valid Twitter URL');
    }
  }

  refreshProfile(): void {
    this.loadUserProfile();
  }

  // Helper method to check if user can edit role (they can't edit their own role)
  canEditRole(): boolean {
    return false; // Users cannot edit their own role in profile
  }
}
