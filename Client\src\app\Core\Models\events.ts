export enum EventType {
  AppearanceOrSigning = 1,
  Attraction = 2,
  CampTripOrRetreat = 3,
  ClassTrainingOrWorkshop = 4,
  ConcertOrPerformance = 5,
  Conference = 6,
  Convention = 7,
  DinnerOrGala = 8,
  FestivalOrFair = 9,
  GamesOrCompetition = 10,
  MeetingOrNetworkingEvent = 11,
  Other = 12,
  PartyOrSocialGathering = 13,
  Rally = 14,
  Screening = 15,
  SeminarOrTalk = 16,
  Tour = 17,
  Tournament = 18,
  TradeShowConsumerShowOrExpo = 19,
}

export enum EventLocationType {
  Venue = 1,
  Online = 2,
}

export enum EventStatus {
  Draft = 1,
  Submitted = 2,
  Approved = 3,
  Rejected = 4,
  Cancelled = 5,
  Completed = 6,
}

export enum Category {
  CareersAndEmployment = 1,
  CommunityResources = 2,
  EarlyChildhood = 3,
  HealthWellness = 4,
  MaternalHealthCare = 5,
  RentalHousing = 6,
}

export interface EventContactDetails {
  id?: number;
  contactName: string;
  contactNo: string;
  email: string;
  website: string;
  eventId?: number;
}

export interface EventLocation {
  id?: number;
  eventId?: number;

  // Physical location fields
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;

  // Online event fields
  meetingId?: string;
  passcode?: string;
}

export interface Event {
  id: number;

  // Basic Info
  title: string;
  type?: EventType;
  typeName?: string;
  category: string;
  capacity?: number;
  description: string;
  eventImageUrl?: string;
  eventImagePath?: string;
  requiresRegistration: boolean;

  // Date and Time
  eventStarts: Date;
  eventEnds: Date;
  startTime?: string;
  endTime?: string;
  displayStartTime: boolean;
  displayEndTime: boolean;

  // Location Type
  locationType: EventLocationType;
  locationTypeName?: string;

  // Status and Approval
  status?: EventStatus;
  statusName?: string;
  isApproved: boolean;
  submittedOn?: Date;
  reviewedOn?: Date;
  reviewedById?: string;
  reviewedByName?: string;
  rejectionReason?: string;

  // Submitter Information
  submitterName?: string;

  // Organizer Information
  organizerId?: string;
  organizerName?: string;

  // Timestamps
  createdAt: Date;
  updatedAt?: Date;

  // Navigation properties
  location: EventLocation;
  contactDetails: EventContactDetails;
}

export interface EventRequest {
  id?: number; // Only for updates

  // Basic Info
  title: string;
  type: EventType;
  category: string;
  capacity?: number;
  description: string;
  eventImage?: File;
  eventImagePath?: string;
  requiresRegistration: boolean;

  // Date and Time
  eventStarts: Date | string;
  eventEnds: Date | string;
  startTime?: string | null;
  endTime?: string | null;
  displayStartTime: boolean;
  displayEndTime: boolean;

  // Location Type
  locationType: EventLocationType;

  // Status and Approval
  status?: EventStatus;
  skipApproval?: boolean;
  isApproved?: boolean;
  submittedOn?: Date | string;

  // Organizer Information
  organizerId?: string;

  // Navigation properties
  location: EventLocation;
  contactDetails: EventContactDetails;
}
