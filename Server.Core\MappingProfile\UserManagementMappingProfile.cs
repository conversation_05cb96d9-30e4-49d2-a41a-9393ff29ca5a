﻿using AutoMapper;
using Server.Core.Entities.UserManagement.RegisterModel;
using Server.Core.Entities.UserManagement.UserDetailsModel;
using Server.Core.Entities.UserManagement.UserModel;

namespace Server.Core.MappingProfile {
    public class UserManagementMappingProfile : Profile {

        public UserManagementMappingProfile() {
            // Map Register to ApplicationUser
            CreateMap<Register, ApplicationUser>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(_ => DateTime.UtcNow));

            CreateMap<UserDetails, ApplicationUser>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(_ => DateTime.UtcNow));
        }
    }
}
