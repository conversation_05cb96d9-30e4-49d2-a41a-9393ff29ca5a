import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { LoginRequest } from '../Models/login-request';
import { catchError, Observable, tap, throwError } from 'rxjs';
import { AuthResponse } from '../Models/auth-response';
import { ApiError } from '../Models/validation-error';
import { environment } from '../../../environments/environment';
import { UserAvatarService } from './user-avatar.service';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private readonly API_URL = `${environment.apiUrl}/api/Account`;
  private readonly TOKEN_KEY = 'token';
  private readonly USER_INFO_KEY = 'userInfo';

  constructor(
    private readonly HttpCLient: HttpClient,
    private userAvatarService: UserAvatarService,
  ) {}

  login(data: LoginRequest): Observable<any> {
    return this.HttpCLient.post<any>(`${this.API_URL}/Login`, data).pipe(
      tap((res) => {
        if (res.isSuccess && res.token) {
          this.setToken(res.token);
          this.setUserInfo(this.decodeToken(res.token));

          // Set a random avatar when user logs in
          this.userAvatarService.setRandomAvatar();
        }
      }),
      catchError((err) => this.handleError(err)),
    );
  }

  loginWith2FA(userId: string, otp: string): Observable<AuthResponse> {
    return this.HttpCLient.post<AuthResponse>(`${this.API_URL}/VerifyOtp`, {
      userId,
      otp,
    }).pipe(
      tap((response) => {
        if (response.isSuccess) {
          this.setToken(response.token);
          this.setUserInfo(this.decodeToken(response.token));

          // Set a random avatar when user logs in with 2FA
          this.userAvatarService.setRandomAvatar();
        }
      }),
      catchError((err) => this.handleError(err)),
    );
  }

  resendOtp(userId: string): Observable<AuthResponse> {
    return this.HttpCLient.post<AuthResponse>(`${this.API_URL}/ResendOtp`, {
      userId,
    }).pipe(catchError((err) => this.handleError(err)));
  }

  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  private setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  getUserInfo(): any {
    const userInfo = localStorage.getItem(this.USER_INFO_KEY);
    return userInfo ? JSON.parse(userInfo) : null;
  }

  private setUserInfo(userInfo: any): void {
    const mappedUserInfo = {
      id: userInfo.nameid || userInfo.sub || userInfo.id,
      name: userInfo.unique_name || userInfo.name,
      email: userInfo.email,
      role: userInfo.role,
      isActive: userInfo.isActive,
      phoneNumber: userInfo.phoneNumber,
      website: userInfo.website,
      description: userInfo.description,
      facebook: userInfo.facebook,
      twitter: userInfo.twitter,
    };

    localStorage.setItem(this.USER_INFO_KEY, JSON.stringify(mappedUserInfo));
  }

  decodeToken(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(function (c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join(''),
      );
      const decoded = JSON.parse(jsonPayload);

      return decoded;
    } catch (e) {
      return null;
    }
  }

  debugToken(): void {
    const token = this.getToken();
    if (token) {
      const decoded = this.decodeToken(token);
    } else {
    }
  }

  isTokenExpired(): boolean {
    const token = this.getToken();
    if (!token) return true;

    const decodedToken = this.decodeToken(token);
    const expirationTime = decodedToken?.exp ? decodedToken.exp * 1000 : 0;
    return Date.now() > expirationTime;
  }

  isLoggedIn(): boolean {
    const token = this.getToken();
    return token !== null && !this.isTokenExpired();
  }

  refreshSession(): void {
    if (this.isTokenExpired()) {
      this.logout();
    } else {
      const token = this.getToken();
      if (token) {
        const decodedToken = this.decodeToken(token);
        if (decodedToken) {
          this.setUserInfo(decodedToken);
          return;
        }
      }
    }
  }

  refreshUserInfoFromToken(): boolean {
    const token = this.getToken();
    if (token && !this.isTokenExpired()) {
      const decodedToken = this.decodeToken(token);
      if (decodedToken) {
        this.setUserInfo(decodedToken);
        return true;
      }
    }
    return false;
  }

  logout(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_INFO_KEY);
  }

  hasRole(role: string): boolean {
    const userInfo = this.getUserInfo();
    if (!userInfo || !userInfo.role) {
      return false;
    }

    if (Array.isArray(userInfo.role)) {
      return userInfo.role.includes(role);
    } else {
      return userInfo.role === role;
    }
  }

  public handleError(error: HttpErrorResponse) {
    const apiError = error.error;
    let errorMessage = 'An unexpected error occurred. Please try again.';

    if (error.error instanceof ErrorEvent) {
      errorMessage = error.error.message;
    } else {
      if (error.status === 400) {
        if (apiError?.message) {
          //
          errorMessage = apiError.message;
        } else if (apiError?.errors && typeof apiError.errors === 'object') {
          const errorItems = Object.entries(apiError.errors).map(
            ([key, messages]) => `${key}: ${(messages as string[]).join(', ')}`,
          );
          errorMessage = `Validation Errors: ${errorItems.join('; ')}`;
        } else if (Array.isArray(apiError)) {
          const errorItems = apiError.map(
            (err: { code: string; description: string }) => err.description,
          );
          errorMessage = `Please fix the following issues: ${errorItems.join('; ')}`;
        } else {
          errorMessage = 'Registration failed due to validation issues.';
        }
      } else if (error.status === 401) {
        errorMessage = 'Invalid credentials. Please check email and password.';
      } else {
        errorMessage =
          apiError?.message ||
          `Server error: ${error.status}. Please try again later.`;
      }
    }

    return throwError(() => apiError?.message || errorMessage);
  }
}
