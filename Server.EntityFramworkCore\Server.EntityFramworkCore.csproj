﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="4.0.0" />
    <PackageReference Include="AWSSDK.S3" Version="4.0.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.15" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.15" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.15" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.15">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Identity.Core" Version="8.0.15" />
    <PackageReference Include="MimeKit" Version="4.11.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Server.Core\Server.Core.csproj" />
  </ItemGroup>

</Project>
