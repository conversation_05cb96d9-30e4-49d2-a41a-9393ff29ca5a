import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../../Core/Services/auth.service';
import { MessageService } from 'primeng/api';
import { NotificationService } from '../../../Core/Services/notification.service';

@Component({
  selector: 'app-otp',
  standalone: false,
  templateUrl: './otp.component.html',
  styleUrls: ['./otp.component.scss'],
  providers: [MessageService],
})
export class OtpComponent implements OnInit, OnDestroy {
  otpForm!: FormGroup;
  isLoading: boolean = false;
  errorMessage: string | null = null;
  userId: string = '';
  remainingTime: number = 30;
  private timer: any;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private messageService: MessageService,
    private notificationService: NotificationService,
  ) {
    const nav = this.router.getCurrentNavigation();
    this.userId = nav?.extras?.state?.['userId'] || '';
    if (!this.userId) {
      this.router.navigate(['/auth/login']);
    }
  }

  ngOnInit(): void {
    this.initForm();
    this.startTimer();
  }

  ngOnDestroy(): void {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }

  private initForm(): void {
    this.otpForm = this.fb.group({
      otp: [
        '',
        [
          Validators.required,
          Validators.minLength(5),
          Validators.maxLength(5),
          Validators.pattern('^[0-9]*$'),
        ],
      ],
    });
  }

  private startTimer(): void {
    this.timer = setInterval(() => {
      if (this.remainingTime > 0) {
        this.remainingTime--;
      } else {
        this.handleOtpExpiration();
      }
    }, 1000);
  }

  private handleOtpExpiration(): void {
    clearInterval(this.timer);
    this.messageService.add({
      severity: 'info',
      summary: 'Resend OTP Available',
      detail:
        'You can now resend the OTP if needed. Your current OTP is still valid for 5 minutes from when it was sent.',
      life: 5000,
    });
    // We don't set an error message here since the OTP is still valid
  }

  get formattedTime(): string {
    return `${this.remainingTime}s`;
  }

  onVerify(): void {
    if (this.otpForm.invalid) {
      Object.keys(this.otpForm.controls).forEach((key) => {
        const control = this.otpForm.get(key);
        if (control?.invalid) {
          control.markAsTouched();
        }
      });
      return;
    }

    // We no longer check for remainingTime <= 0 since the OTP is valid for 5 minutes
    // even after the 30-second timer for resend button expires

    this.isLoading = true;
    const otp = this.otpForm.value.otp;

    this.authService.loginWith2FA(this.userId, otp).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.messageService.add({
            severity: 'success',
            summary: 'OTP Verified',
            detail: response.message,
            life: 4000,
          });
          clearInterval(this.timer);

          // Initialize notification service after successful OTP verification
          // This will ensure notifications are only loaded after authentication is complete
          setTimeout(() => {
            this.notificationService.initialize();
            this.router.navigate(['/dashboard']);
          }, 100);
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Verification Failed',
            detail: response.message,
            life: 4000,
          });
          this.errorMessage = response.message;
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Verification Failed',
          detail: error.error?.message || 'Invalid or expired OTP',
          life: 4000,
        });
        this.isLoading = false;
        this.errorMessage = error.message;
      },
    });
  }

  resendOtp(): void {
    if (!this.userId) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'User ID not found. Please return to login.',
        life: 4000,
      });
      return;
    }

    this.isLoading = true;
    this.authService.resendOtp(this.userId).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          // Reset the timer
          clearInterval(this.timer);
          this.remainingTime = 30;
          this.startTimer();

          // Clear any error messages
          this.errorMessage = null;

          // Reset the OTP form
          this.otpForm.reset();

          this.messageService.add({
            severity: 'success',
            summary: 'OTP Sent',
            detail:
              'A new OTP has been sent to your email. It is valid for 5 minutes.',
            life: 4000,
          });
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Failed',
            detail: response.message || 'Failed to resend OTP',
            life: 4000,
          });
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: error.error?.message || 'Failed to resend OTP',
          life: 4000,
        });
        this.isLoading = false;
      },
    });
  }
}
