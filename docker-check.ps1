# PowerShell script to check the status of Docker containers

Write-Host "Checking FullStackSFL Docker containers..." -ForegroundColor Cyan

# Check if Docker is running
try {
    docker info | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
        exit
    }
} catch {
    Write-Host "Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit
}

# Check container status
$containers = docker-compose ps --format json | ConvertFrom-Json

if ($containers.Count -eq 0) {
    Write-Host "No containers are running. Use docker-start.ps1 to start the containers." -ForegroundColor Yellow
    exit
}

Write-Host "Container Status:" -ForegroundColor Green
foreach ($container in $containers) {
    $name = $container.Name
    $state = $container.State
    $status = $container.Status
    
    if ($state -eq "running") {
        $color = "Green"
    } else {
        $color = "Red"
    }
    
    Write-Host "  - $name : $state ($status)" -ForegroundColor $color
}

# Check if frontend is accessible
Write-Host "`nChecking frontend accessibility..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:4200" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "  Frontend is accessible at http://localhost:4200" -ForegroundColor Green
    } else {
        Write-Host "  Frontend returned status code: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "  Frontend is not accessible at http://localhost:4200" -ForegroundColor Red
}

# Check if backend is accessible
Write-Host "`nChecking backend accessibility..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5020/api/healthcheck" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "  Backend is accessible at http://localhost:5020/api/healthcheck" -ForegroundColor Green
        Write-Host "  Response: $($response.Content)" -ForegroundColor White
    } else {
        Write-Host "  Backend returned status code: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "  Backend is not accessible at http://localhost:5020/api/healthcheck" -ForegroundColor Red
}

# Check if database is accessible
Write-Host "`nChecking database container..." -ForegroundColor Cyan
$dbContainer = $containers | Where-Object { $_.Name -like "*db*" }
if ($dbContainer) {
    if ($dbContainer.State -eq "running") {
        Write-Host "  Database container is running" -ForegroundColor Green
    } else {
        Write-Host "  Database container is not running" -ForegroundColor Red
    }
} else {
    Write-Host "  Database container not found" -ForegroundColor Red
}

Write-Host "`nTo view container logs, run: docker-compose logs" -ForegroundColor Cyan
Write-Host "To restart containers, run: docker-compose restart" -ForegroundColor Cyan
