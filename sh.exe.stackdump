Stack trace:
Frame         Function      Args
0007FFFFB4F0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA3F0) msys-2.0.dll+0x1FE8E
0007FFFFB4F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFB7C8) msys-2.0.dll+0x67F9
0007FFFFB4F0  000210046832 (000210286019, 0007FFFFB3A8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB4F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB4F0  000210068E24 (0007FFFFB500, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFB7D0  00021006A225 (0007FFFFB500, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 sh.exe
7FFB31AC0000 ntdll.dll
7FFB316E0000 KERNEL32.DLL
7FFB2F120000 KERNELBASE.dll
7FFB30B60000 USER32.dll
7FFB2EE60000 win32u.dll
7FFB2FAE0000 GDI32.dll
7FFB2EFE0000 gdi32full.dll
7FFB2EF30000 msvcp_win.dll
7FFB2EC50000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB31200000 advapi32.dll
7FFB31860000 msvcrt.dll
7FFB317B0000 sechost.dll
7FFB2FB10000 RPCRT4.dll
7FFB2E360000 CRYPTBASE.DLL
7FFB2EE90000 bcryptPrimitives.dll
7FFB312C0000 IMM32.DLL
