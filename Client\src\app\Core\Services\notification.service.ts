import { Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, Observable, map } from 'rxjs';
import { EventsService } from './events.service';
import { Event } from '../Models/events';
import { EventPaginationParams } from '../Models/pagination.events.interface';
import { AuthService } from './auth.service';
import { Router } from '@angular/router';
import { DateUtilsService } from './date-utils.service';

export interface Notification {
  id: number;
  title: string;
  message: string;
  timestamp: Date;
  status:
    | 'Draft'
    | 'Pending Review'
    | 'Approved'
    | 'Rejected'
    | 'Event Has Started';
  eventId: number;
  submitterName: string;
  organizerName?: string;
  timeAgo: string;
  startDate: string;
  endDate: string;
  startTime: string | undefined;
  endTime: string | undefined;
  displayStartTime: boolean;
  displayEndTime: boolean;
  eventStarts?: Date; // Added to check if event has started
  isRead?: boolean; // Added for read status management
}

export interface NotificationGroup {
  title: string;
  notifications: Notification[];
}

@Injectable({
  providedIn: 'root',
})
export class NotificationService implements OnDestroy {
  private notifications = new BehaviorSubject<Notification[]>([]);
  private isLoading = new BehaviorSubject<boolean>(false);
  private unreadCount = new BehaviorSubject<number>(0);
  private isInitialized = false;
  private isExplicitRefresh = false;
  private readonly STORAGE_KEY = 'notification_read_ids';
  private readonly DELETED_NOTIFICATIONS_KEY = 'deleted_notification_ids';

  constructor(
    private eventsService: EventsService,
    private authService: AuthService,
    private router: Router,
    private dateUtils: DateUtilsService,
  ) {}

  initialize(): void {
    if (this.isInitialized || !this.authService.isLoggedIn()) {
      return;
    }

    // Check if we're on the login or OTP page - don't initialize in that case
    const currentUrl = this.router.url;
    if (
      currentUrl.includes('/auth/login') ||
      currentUrl.includes('/auth/VerifyOtp')
    ) {
      return;
    }

    this.isInitialized = true;
    // Load notifications immediately on initialization - this is the only automatic load
    this.loadNotifications();
  }

  getNotifications(): Observable<Notification[]> {
    return this.notifications.asObservable();
  }

  getNotificationGroups(): Observable<NotificationGroup[]> {
    return this.notifications
      .asObservable()
      .pipe(
        map((notifications) => this.groupNotificationsByDate(notifications)),
      );
  }

  getIsLoading(): Observable<boolean> {
    return this.isLoading.asObservable();
  }

  getUnreadCount(): Observable<number> {
    return this.unreadCount.asObservable();
  }

  loadNotifications(): void {
    // Skip loading if not authenticated
    if (!this.authService.isLoggedIn()) {
      this.notifications.next([]);
      this.unreadCount.next(0);
      this.isLoading.next(false);
      return;
    }

    // Skip loading if we're on the event list page to avoid duplicate API calls
    // But allow refresh when explicitly called (e.g., after event creation)
    if (
      this.router.url.includes('/events/event-list') &&
      !this.isExplicitRefresh
    ) {
      return;
    }

    // Reset explicit refresh flag
    this.isExplicitRefresh = false;

    this.isLoading.next(true);

    const params: EventPaginationParams = {
      pageNumber: 1,
      pageSize: 20,
      sortField: 'submittedOn',
      sortOrder: 'desc',
      filters: {
        eventStatus: 'Draft,Pending Review,Approved,Rejected',
      },
    };

    this.eventsService.getEvents(params).subscribe({
      next: (response) => {
        if (response && response.items) {
          const allNotifications = this.mapEventsToNotifications(
            response.items,
          );
          const readIds = this.getReadNotificationIds();
          const deletedIds = this.getDeletedNotificationIds();

          // Filter out deleted notifications and set read status
          const activeNotifications = allNotifications
            .filter((n) => !deletedIds.includes(n.id))
            .map((n) => ({
              ...n,
              isRead: readIds.includes(n.id),
            }));

          const unreadNotifications = activeNotifications.filter(
            (n) => !n.isRead,
          );

          this.notifications.next(activeNotifications);
          this.unreadCount.next(unreadNotifications.length);
          this.isLoading.next(false);
        } else {
          this.notifications.next([]);
          this.unreadCount.next(0);
          this.isLoading.next(false);
        }
      },
      error: (error) => {
        this.notifications.next([]);
        this.unreadCount.next(0);
        this.isLoading.next(false);
      },
    });
  }

  markAllAsRead(): void {
    const currentNotifications = this.notifications.value;
    if (currentNotifications.length > 0) {
      const notificationIds = currentNotifications.map((n) => n.id);
      this.saveReadNotificationIds(notificationIds);

      // Update the notifications array to mark all as read
      const updatedNotifications = currentNotifications.map((n) => ({
        ...n,
        isRead: true,
      }));
      this.notifications.next(updatedNotifications);

      // Update unread count to 0
      this.unreadCount.next(0);
    }
  }

  // Mark individual notification as read
  markNotificationAsRead(notificationId: number): void {
    const readIds = this.getReadNotificationIds();
    if (!readIds.includes(notificationId)) {
      this.saveReadNotificationIds([notificationId]);

      // Update the current notifications array
      const currentNotifications = this.notifications.value.map((n) =>
        n.id === notificationId ? { ...n, isRead: true } : n,
      );
      this.notifications.next(currentNotifications);

      // Update unread count
      const unreadCount = currentNotifications.filter((n) => !n.isRead).length;
      this.unreadCount.next(unreadCount);
    }
  }

  // Mark all currently loaded notifications as read (called when panel is closed)
  markAllCurrentNotificationsAsRead(): void {
    const currentNotifications = this.notifications.value;
    if (currentNotifications.length > 0) {
      // Get only the unread notification IDs to save to localStorage
      const unreadNotificationIds = currentNotifications
        .filter((n) => !n.isRead)
        .map((n) => n.id);

      if (unreadNotificationIds.length > 0) {
        // Save the newly read notification IDs to localStorage
        this.saveReadNotificationIds(unreadNotificationIds);

        // Update all current notifications to mark them as read
        const updatedNotifications = currentNotifications.map((n) => ({
          ...n,
          isRead: true,
        }));
        this.notifications.next(updatedNotifications);

        // Update unread count to 0 since all current notifications are now read
        this.unreadCount.next(0);
      }
    }
  }

  // Delete individual notification
  deleteNotification(notificationId: number): void {
    // Add to deleted notifications list
    const deletedIds = this.getDeletedNotificationIds();
    if (!deletedIds.includes(notificationId)) {
      this.saveDeletedNotificationIds([notificationId]);
    }

    // Remove from current notifications
    const currentNotifications = this.notifications.value.filter(
      (n) => n.id !== notificationId,
    );
    this.notifications.next(currentNotifications);

    // Update unread count
    const unreadCount = currentNotifications.filter((n) => !n.isRead).length;
    this.unreadCount.next(unreadCount);
  }

  // Clear all notifications
  clearAllNotifications(): void {
    const currentNotifications = this.notifications.value;
    if (currentNotifications.length > 0) {
      // Add all current notification IDs to deleted list
      const notificationIds = currentNotifications.map((n) => n.id);
      this.saveDeletedNotificationIds(notificationIds);

      // Clear the notifications array
      this.notifications.next([]);

      // Reset unread count to 0
      this.unreadCount.next(0);
    }
  }

  // Public method to refresh notifications - can be called from other components
  refreshNotifications(): void {
    this.isExplicitRefresh = true;
    this.loadNotifications();
  }

  // Reset notification system (useful for logout)
  reset(): void {
    this.notifications.next([]);
    this.unreadCount.next(0);
    this.isInitialized = false;
  }

  // Local storage methods for tracking read notifications
  private getReadNotificationIds(): number[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  private saveReadNotificationIds(ids: number[]): void {
    try {
      const existingIds = this.getReadNotificationIds();
      const allIds = [...new Set([...existingIds, ...ids])];
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(allIds));
    } catch {
      // Silently fail if localStorage is not available
    }
  }

  // Local storage methods for tracking deleted notifications
  private getDeletedNotificationIds(): number[] {
    try {
      const stored = localStorage.getItem(this.DELETED_NOTIFICATIONS_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  private saveDeletedNotificationIds(ids: number[]): void {
    try {
      const existingIds = this.getDeletedNotificationIds();
      const allIds = [...new Set([...existingIds, ...ids])];
      localStorage.setItem(
        this.DELETED_NOTIFICATIONS_KEY,
        JSON.stringify(allIds),
      );
    } catch {
      // Silently fail if localStorage is not available
    }
  }

  ngOnDestroy(): void {
    // No interval to clear anymore
  }

  private mapEventsToNotifications(events: Event[]): Notification[] {
    return events.map((event) => {
      // Initialize with a default value to avoid "used before assigned" error
      let status:
        | 'Draft'
        | 'Pending Review'
        | 'Approved'
        | 'Rejected'
        | 'Event Has Started' = 'Draft';

      // Check if the event has started
      const hasEventStarted = this.hasEventStarted(event);

      switch (event.statusName) {
        case 'Draft':
          status = 'Draft';
          break;
        case 'Submitted':
        case 'Pending Review':
          status = 'Pending Review';
          break;
        case 'Approved':
          // If event is approved and has started, show "Event Has Started" status
          status = hasEventStarted ? 'Event Has Started' : 'Approved';
          break;
        case 'Rejected':
          status = 'Rejected';
          break;
      }

      // Generate appropriate message based on status
      let message = '';
      switch (status) {
        case 'Draft':
          message = 'has been created by';
          break;
        case 'Pending Review':
          message = 'has been submitted for review by';
          break;
        case 'Approved':
          message = 'has been approved and added to the schedule by';
          break;
        case 'Rejected':
          message = 'has been rejected by';
          break;
        case 'Event Has Started':
          message = 'has started';
          break;
      }

      // Format dates in the format "14 Sep 2023, 5:30 PM" - This is for display purposes only
      // Convert UTC dates to IST before displaying
      const startDate = this.dateUtils.formatDateForDisplay(event.eventStarts);
      const endDate = this.dateUtils.formatDateForDisplay(event.eventEnds);

      // Use createdAt for both timestamp and timeAgo to ensure consistency
      const timestamp = event.createdAt || new Date();

      return {
        id: event.id,
        title: event.title,
        message: message,
        timestamp: timestamp,
        status: status,
        eventId: event.id,
        submitterName: event.submitterName || 'Unknown',
        organizerName: event.organizerName || 'Unknown',
        timeAgo: this.dateUtils.getTimeAgo(timestamp),
        startDate: startDate,
        endDate: endDate,
        startTime: event.startTime,
        endTime: event.endTime,
        displayStartTime: event.displayStartTime,
        displayEndTime: event.displayEndTime,
        eventStarts: event.eventStarts,
        isRead: false, // Will be set correctly in loadNotifications
      };
    });
  }

  // Check if the event has already started
  private hasEventStarted(event: Event): boolean {
    if (!event || !event.eventStarts) return false;

    // Convert UTC event start time to IST before comparing
    const eventStartTime = this.dateUtils
      .convertUtcToIst(event.eventStarts)
      .getTime();
    const now = new Date().getTime();

    return now >= eventStartTime;
  }

  private groupNotificationsByDate(
    notifications: Notification[],
  ): NotificationGroup[] {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const groups: NotificationGroup[] = [];

    // Today's notifications
    const todayNotifications = notifications.filter((n) => {
      // Convert UTC timestamp to IST before comparing
      const date = this.dateUtils.convertUtcToIst(n.timestamp);
      date.setHours(0, 0, 0, 0);
      return date.getTime() === today.getTime();
    });

    if (todayNotifications.length > 0) {
      // Sort notifications by timestamp (newest first)
      todayNotifications.sort((a, b) => {
        return (
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
      });

      groups.push({
        title: 'Today',
        notifications: todayNotifications,
      });
    }

    // Yesterday's notifications
    const yesterdayNotifications = notifications.filter((n) => {
      // Convert UTC timestamp to IST before comparing
      const date = this.dateUtils.convertUtcToIst(n.timestamp);
      date.setHours(0, 0, 0, 0);
      return date.getTime() === yesterday.getTime();
    });

    if (yesterdayNotifications.length > 0) {
      // Sort notifications by timestamp (newest first)
      yesterdayNotifications.sort((a, b) => {
        return (
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
      });

      groups.push({
        title: 'Yesterday',
        notifications: yesterdayNotifications,
      });
    }

    // Older notifications
    const olderNotifications = notifications.filter((n) => {
      // Convert UTC timestamp to IST before comparing
      const date = this.dateUtils.convertUtcToIst(n.timestamp);
      date.setHours(0, 0, 0, 0);
      return date.getTime() < yesterday.getTime();
    });

    if (olderNotifications.length > 0) {
      // Sort notifications by timestamp (newest first)
      olderNotifications.sort((a, b) => {
        return (
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
      });

      groups.push({
        title: 'Older',
        notifications: olderNotifications,
      });
    }

    return groups;
  }
}
