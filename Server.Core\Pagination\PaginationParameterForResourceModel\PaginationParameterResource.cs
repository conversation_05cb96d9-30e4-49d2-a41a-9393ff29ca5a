﻿namespace Server.Core.Pagination.PaginationParameterForResourceModel {
    public class PaginationParameterEvents {

        private const int MaxPageSize = 50;
        private int _pageSize = 10;

        public int PageNumber { get; set; } = 1;

        public int PageSize {
            get => _pageSize;
            set => _pageSize = value > MaxPageSize ? MaxPageSize : value;
        }

        public string? SortField { get; set; }
        public string? SortOrder { get; set; }

        // Filter properties
        public string? OrganizationalTitle { get; set; }
        public string? Category { get; set; }
        public string? ResourceType { get; set; }

    }
}
