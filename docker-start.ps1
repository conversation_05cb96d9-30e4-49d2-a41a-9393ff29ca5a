# PowerShell script to start the Docker containers

Write-Host "Starting FullStackSFL Docker containers..." -ForegroundColor Green

# Check if .env file exists
if (-not (Test-Path -Path ".env")) {
    Write-Host "Warning: .env file not found. Creating a template .env file..." -ForegroundColor Yellow
    @"
# AWS Credentials
AWS_ACCESS_KEY=your_aws_access_key
AWS_SECRET_KEY=your_aws_secret_key

# Note: In a production environment, you should never commit this file to version control.
"@ | Out-File -FilePath ".env" -Encoding utf8
    Write-Host "Please edit the .env file with your actual AWS credentials before continuing." -ForegroundColor Yellow
    exit
}

# Start the containers
Write-Host "Building and starting containers..." -ForegroundColor Cyan
docker-compose up -d --build

# Check if containers are running
$containers = docker-compose ps
if ($LASTEXITCODE -eq 0) {
    Write-Host "Containers started successfully!" -ForegroundColor Green
    Write-Host "You can access the application at:" -ForegroundColor Cyan
    Write-Host "  - Frontend: http://localhost:4200" -ForegroundColor White
    Write-Host "  - Backend API: http://localhost:5020" -ForegroundColor White
    Write-Host "  - Swagger UI: http://localhost:5020/swagger" -ForegroundColor White
} else {
    Write-Host "Error starting containers. Please check the logs with 'docker-compose logs'." -ForegroundColor Red
}

Write-Host "To stop the containers, run: docker-compose down" -ForegroundColor Cyan
