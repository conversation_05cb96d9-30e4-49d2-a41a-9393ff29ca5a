﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Server.EntityFramworkCore.Migrations
{
    /// <inheritdoc />
    public partial class AddReviewedByField : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ReviewedById",
                table: "Events",
                type: "nvarchar(450)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Events_ReviewedById",
                table: "Events",
                column: "ReviewedById");

            migrationBuilder.AddForeignKey(
                name: "FK_Events_AspNetUsers_ReviewedById",
                table: "Events",
                column: "ReviewedById",
                principalTable: "AspNetUsers",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Events_AspNetUsers_ReviewedById",
                table: "Events");

            migrationBuilder.DropIndex(
                name: "IX_Events_ReviewedById",
                table: "Events");

            migrationBuilder.DropColumn(
                name: "ReviewedById",
                table: "Events");
        }
    }
}
