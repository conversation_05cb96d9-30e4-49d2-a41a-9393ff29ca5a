.logout-btn {
  cursor: pointer !important;
}

.bell-button {
  position: relative;
  font-size: 1.2rem;
}

.notification-panel {
  position: absolute;
  top: 100%;
  right: 0;
  width: 600px;
  max-height: 600px;
  overflow-y: auto;
  z-index: 1000;
  border-radius: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background-color: white;
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  h5 {
    font-size: 18px;
    font-weight: 600;
  }
}

.notification-list {
  padding: 0;
}

.notification-group {
  margin-bottom: 0;
}

.notification-group-header {
  padding: 10px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;

  h6 {
    color: #666;
    font-weight: 600;
  }
}

.notification-item {
  display: flex;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
  align-items: flex-start;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-dot {
  width: 8px;
  height: 8px;
  background-color: #ff0000;
  border-radius: 50%;
  margin-right: 15px;
  margin-top: 6px;
  flex-shrink: 0;
}

.notification-dot-placeholder {
  width: 8px;
  height: 8px;
  margin-right: 15px;
  margin-top: 6px;
  flex-shrink: 0;
}

.notification-content {
  flex-grow: 1;
  padding-right: 150px; // Increased to accommodate delete button
  font-size: 14px;
  line-height: 1.5;
}

.event-link,
.organizer-link {
  color: #0066cc;
  text-decoration: none;
  font-weight: 500;
  &:hover {
    text-decoration: underline;
  }
}

.notification-time {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
}

.notification-delete-btn {
  position: absolute;
  right: 150px; // Position between content and status
  top: 15px;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: #999;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;

  &:hover {
    background-color: #f8d7da;
    color: #721c24;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
  }
}

.notification-status {
  position: absolute;
  right: 20px;
  top: 15px;
  padding: 0.15rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.7rem;
  font-weight: 500;
  white-space: nowrap;
  height: fit-content;

  &.draft {
    background-color: #f5f5f5; // Light gray background
    color: #757575; // Dark gray text
  }

  &.pending-review {
    background-color: #fff8e1; // Light yellow background
    color: #f59e0b; // Dark yellow text
  }

  &.approved {
    background-color: #e8f5e9; // Light green background (matches Figma)
    color: #2e7d32; // Dark green text (matches Figma)
  }

  &.event-started {
    background-color: #e3f2fd; // Light blue background
    color: #1565c0; // Dark blue text
  }

  &.rejected {
    background-color: #ffebee; // Light red background (matches Figma)
    color: #c62828; // Dark red text (matches Figma)
  }
}

// Badge styles for notification count
.badge {
  font-size: 0.65rem;
  padding: 0.25em 0.5em;
}

// Clear All button styling
.clear-all-btn {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
  }

  i {
    font-size: 11px;
  }
}
