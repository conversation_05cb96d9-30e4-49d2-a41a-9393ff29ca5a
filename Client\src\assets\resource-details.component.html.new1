<!-- Toast for notifications -->
<p-toast></p-toast>

<!-- Loading Spinner -->
<div
  *ngIf="isLoading"
  class="d-flex justify-content-center align-items-center"
  style="min-height: 400px"
>
  <p-progressSpinner
    strokeWidth="4"
    [style]="{ width: '50px', height: '50px' }"
  ></p-progressSpinner>
</div>

<!-- Error Message -->
<div *ngIf="!isLoading && error" class="container mt-5">
  <div class="alert alert-danger">
    <h4 class="alert-heading">{{ error }}</h4>
    <p>
      The resource you're looking for could not be found. This might be because:
    </p>
    <ul>
      <li>The resource ID is invalid</li>
      <li>The resource has been deleted</li>
      <li>There was a connection issue with the server</li>
    </ul>
    <hr />
    <div class="d-flex justify-content-between">
      <button class="btn btn-outline-danger" (click)="loadResourceDetails()">
        <i class="bi bi-arrow-clockwise"></i> Retry
      </button>
      <button class="btn btn-primary" (click)="goBack()">
        <i class="bi bi-arrow-left"></i> Back to Resources
      </button>
    </div>
  </div>
</div>

<!-- Resource Details Content -->
<div
  *ngIf="!isLoading && !error && resource"
  class="resource-details-container"
>
  <!-- Header with Back Button and Edit Button -->
  <div class="d-flex justify-content-between align-items-center mb-3">
    <div class="d-flex align-items-center">
      <button (click)="goBack()" class="btn btn-link text-dark p-0 me-2">
        <i class="bi bi-arrow-left fs-4"></i>
      </button>
      <h2 class="mb-0">Resource Details</h2>
    </div>
    <button class="btn btn-danger" (click)="editResource()">
      <i class="bi bi-pencil me-1"></i> Edit
    </button>
  </div>

  <!-- Main Image Banner -->
  <div class="card mb-4 border-0 shadow-sm overflow-hidden">
    <div class="position-relative">
      <img
        [src]="
          getFullImagePath(resource.resourceImageUrl) ||
          'assets/images/default-banner.jpg'
        "
        alt="Resource Banner"
        class="w-100 resource-banner-img"
      />
    </div>
  </div>

  <!-- Organization Info with Logo -->
  <div class="card mb-4 border-0 shadow-sm">
    <div class="card-body">
      <div class="d-flex align-items-start">
        <div class="organization-logo me-3">
          <img
            [src]="
              getFullImagePath(resource.resourceLogoUrl) ||
              'assets/images/default-logo.png'
            "
            alt="Organization Logo"
            class="rounded-circle"
            width="80"
            height="80"
          />
        </div>
        <div>
          <h3 class="mb-1">{{ resource.organizationTitle }}</h3>
          <p class="text-muted mb-1">{{ resource.subTitle }}</p>
          <div>
            <span class="badge bg-light text-dark me-2">
              {{ resource.resourceCategory }}
            </span>
            <span class="badge bg-primary text-white">
              {{ resource.resourceTypeName || "Unknown" }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Location Section -->
  <div class="card mb-4 border-0 shadow-sm">
    <div class="card-body">
      <h5 class="mb-3">Location</h5>
      <div class="d-flex align-items-start">
        <i class="bi bi-geo-alt me-2 text-danger"></i>
        <span
          >{{ resource.address }}, {{ resource.city }}, {{ resource.state }}
          {{ resource.zipCode }}</span
        >
      </div>
    </div>
  </div>

  <!-- Description Section -->
  <div class="card mb-4 border-0 shadow-sm">
    <div class="card-body">
      <h5 class="mb-3">Long Description</h5>
      <p>{{ resource.longDescription }}</p>
    </div>
  </div>

  <!-- Services Section -->
  <div class="card mb-4 border-0 shadow-sm">
    <div class="card-body">
      <h5 class="mb-3">Services</h5>
      <div class="row">
        <div class="col-md-6 mb-2" *ngFor="let service of resource.services">
          <div class="d-flex align-items-center">
            <i
              class="bi bi-circle-fill me-2 text-primary"
              style="font-size: 0.5rem"
            ></i>
            <span>{{ service }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Contact Details Section -->
  <div class="card mb-4 border-0 shadow-sm">
    <div class="card-body">
      <h5 class="mb-3">Contact Details</h5>
      <div class="row">
        <div class="col-md-6 mb-3">
          <div class="d-flex align-items-start">
            <i class="bi bi-person me-2"></i>
            <div>
              <p class="text-muted mb-1">Contact Name</p>
              <p class="mb-0 fw-medium">{{ resource.organizationTitle }}</p>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-3">
          <div class="d-flex align-items-start">
            <i class="bi bi-telephone me-2"></i>
            <div>
              <p class="text-muted mb-1">Contact No</p>
              <p class="mb-0 fw-medium">
                {{ resource.contactDetails.ContactNo }}
              </p>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-3">
          <div class="d-flex align-items-start">
            <i class="bi bi-envelope me-2"></i>
            <div>
              <p class="text-muted mb-1">Email</p>
              <p class="mb-0 fw-medium">{{ resource.contactDetails.email }}</p>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-3">
          <div class="d-flex align-items-start">
            <i class="bi bi-globe me-2"></i>
            <div>
              <p class="text-muted mb-1">Website</p>
              <p class="mb-0 fw-medium">
                <a
                  [href]="resource.contactDetails.website"
                  target="_blank"
                  class="text-decoration-none"
                >
                  {{ resource.contactDetails.website }}
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Social Media Section -->
  <div class="card mb-4 border-0 shadow-sm">
    <div class="card-body">
      <h5 class="mb-3">Social Media</h5>
      <div class="row">
        <div class="col-md-6 mb-3" *ngIf="resource.socialMedia?.facebook">
          <div class="d-flex align-items-start">
            <i class="bi bi-facebook me-2"></i>
            <div>
              <p class="text-muted mb-1">Facebook</p>
              <p class="mb-0 fw-medium">
                <a
                  [href]="resource.socialMedia.facebook"
                  target="_blank"
                  class="text-decoration-none"
                >
                  {{ resource.socialMedia.facebook }}
                </a>
              </p>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-3" *ngIf="resource.socialMedia?.instagram">
          <div class="d-flex align-items-start">
            <i class="bi bi-instagram me-2"></i>
            <div>
              <p class="text-muted mb-1">Instagram</p>
              <p class="mb-0 fw-medium">
                <a
                  [href]="resource.socialMedia.instagram"
                  target="_blank"
                  class="text-decoration-none"
                >
                  {{ resource.socialMedia.instagram }}
                </a>
              </p>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-3" *ngIf="resource.socialMedia?.twitter">
          <div class="d-flex align-items-start">
            <i class="bi bi-twitter me-2"></i>
            <div>
              <p class="text-muted mb-1">Twitter (X)</p>
              <p class="mb-0 fw-medium">
                <a
                  [href]="resource.socialMedia.twitter"
                  target="_blank"
                  class="text-decoration-none"
                >
                  {{ resource.socialMedia.twitter }}
                </a>
              </p>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-3" *ngIf="resource.socialMedia?.linkedin">
          <div class="d-flex align-items-start">
            <i class="bi bi-linkedin me-2"></i>
            <div>
              <p class="text-muted mb-1">Linkedin</p>
              <p class="mb-0 fw-medium">
                <a
                  [href]="resource.socialMedia.linkedin"
                  target="_blank"
                  class="text-decoration-none"
                >
                  {{ resource.socialMedia.linkedin }}
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Partner Resource Section (if applicable) -->
  <div
    class="card mb-4 border-0 shadow-sm"
    *ngIf="resource.resourceTypeName === 'SWPNPartner'"
  >
    <div class="card-body">
      <h5 class="mb-3">Partner Resource</h5>
      <div class="d-flex align-items-start mb-3">
        <div class="partner-logo me-3">
          <img
            src="assets/images/imagine-logo.png"
            alt="Partner Logo"
            width="60"
            height="60"
          />
        </div>
        <div>
          <h6 class="mb-1">Imagine, A Center For Coping With Loss</h6>
          <p class="text-muted mb-1">South Ward Supports Our Families</p>
          <p class="mb-0 badge bg-light text-dark">Community Resources</p>
        </div>
      </div>
      <p>{{ resource.shortDescription }}</p>
    </div>
  </div>
</div>
