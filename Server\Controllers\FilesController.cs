using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Server.Services.S3Services;

namespace Server.API.Controllers {
    [Route("api/[controller]")]
    [ApiController]
    public class FilesController : ControllerBase {
        private readonly IWebHostEnvironment _environment;
        private readonly IS3Service _s3Service;
        private readonly bool _useS3;

        public FilesController(IWebHostEnvironment environment, IS3Service s3Service, IConfiguration configuration) {
            _environment = environment;
            _s3Service = s3Service;

            // Determine if we should use S3 or local storage
            _useS3 = !string.IsNullOrEmpty(configuration["AWS:S3:BucketName"]);
        }

        [HttpGet("{folderName}/{fileName}")]
        [AllowAnonymous]
        public IActionResult GetFile(string folderName, string fileName) {
            try {
                

                // If S3 is configured, use S3 storage
                if (_useS3) {


                    // Construct the key
                    var key = $"{folderName}/{fileName}";


                    // Get the file URL from S3
                    var fileUrl = _s3Service.GetFileUrl(key);


                    if (string.IsNullOrEmpty(fileUrl)) {

                        return NotFound();
                    }


                    return Redirect(fileUrl);
                }
                else {

                    var filePath = Path.Combine(_environment.WebRootPath, folderName, fileName);


                    // Check if file exists
                    if (!System.IO.File.Exists(filePath)) {

                        return NotFound();
                    }

                    // Determine content type based on file extension
                    var contentType = GetContentType(Path.GetExtension(fileName));

                    return PhysicalFile(filePath, contentType);
                }
            }
            catch (Exception ex) {

                if (ex.InnerException != null) {

                }
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        private string GetContentType(string fileExtension) {
            switch (fileExtension.ToLower()) {
                case ".jpg":
                case ".jpeg":
                    return "image/jpeg";
                case ".png":
                    return "image/png";
                case ".gif":
                    return "image/gif";
                case ".pdf":
                    return "application/pdf";
                default:
                    return "application/octet-stream";
            }
        }
    }
}
