:host {
  display: block;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  padding: 0;
  margin: 0;
  position: absolute;
  top: 0;
  left: 0;
}

.login-card {
  width: 400px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.18);
  animation: fadeIn 0.5s ease-in-out;
  transition: all 0.3s ease;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;

  h1 {
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
  }
}

.form-field {
  margin-bottom: 25px;

  .p-input-icon-left {
    width: 100%;
    position: relative;

    i {
      color: rgba(255, 255, 255, 0.8);
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 2;
      font-size: 1.1rem;
    }
  }

  input {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 12px !important;
    color: white !important;
    padding: 12px 15px 12px 45px !important;
    transition: all 0.3s ease;
    height: 50px;
    font-size: 1rem;

    &:focus {
      background: rgba(255, 255, 255, 0.25) !important;
      border-color: rgba(255, 255, 255, 0.5) !important;
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.2) !important;
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }
  }

  .p-error {
    margin-top: 5px;
    color: #ff6b6b;
    font-size: 0.85rem;
  }
}

.form-actions {
  margin-top: 30px;
  display: flex;
  justify-content: center;

  .login-button {
    background: linear-gradient(45deg, #11998e, #38ef7d) !important;
    border: none !important;
    border-radius: 12px !important;
    height: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(56, 239, 125, 0.3);
    transition: all 0.3s ease !important;
    width: 150px !important; /* Set a fixed width for the button */

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(56, 239, 125, 0.4);
    }

    &:disabled {
      opacity: 0.7;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

::ng-deep .p-toast .custom-error-toast {
  background-color: #fce4e4;
  border-left: 5px solid #f44336;
  color: #b71c1c;
  font-family: "Segoe UI", sans-serif;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

::ng-deep .p-toast .custom-error-toast .p-toast-message-text {
  padding: 0.5rem 0.5rem 0.5rem 0;
}

::ng-deep .p-toast .custom-error-toast .p-toast-summary {
  font-weight: 600;
  font-size: 1rem;
  color: #d32f2f;
}

::ng-deep .p-toast .custom-error-toast .p-toast-detail {
  color: #333;
  font-size: 0.95rem;
}

::ng-deep .p-toast .custom-error-toast .p-toast-icon-close {
  color: #d32f2f;
  font-size: 1rem;
}

::ng-deep .p-toast .custom-success-toast {
  background-color: #e8f5e9;
  border-left: 5px solid #2e7d32;
  color: #1b5e20;
  font-family: "Segoe UI", sans-serif;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
}

::ng-deep .p-toast .custom-success-toast .p-toast-summary {
  font-weight: 600;
  font-size: 1rem;
  color: #2e7d32;
}

::ng-deep .p-toast .custom-success-toast .p-toast-detail {
  color: #2e2e2e;
  font-size: 0.95rem;
}

::ng-deep .p-toast .custom-success-toast .p-toast-icon-close {
  color: #2e7d32;
  font-size: 1rem;
}
