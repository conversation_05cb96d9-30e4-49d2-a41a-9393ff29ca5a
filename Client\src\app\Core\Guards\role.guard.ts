import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { AuthService } from '../Services/auth.service';

@Injectable({
  providedIn: 'root',
})
export class RoleGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): boolean {
    // First check if the user is authenticated
    if (!this.authService.isLoggedIn()) {
      this.router.navigate(['/authentication/login'], {
        queryParams: { returnUrl: state.url },
      });
      return false;
    }

    // Get the required roles from the route data
    const requiredRoles = route.data['roles'] as Array<string>;

    // If no roles are required, allow access
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    // Get the user info from the auth service
    const userInfo = this.authService.getUserInfo();

    // Check if the user has any of the required roles
    if (userInfo && userInfo.role) {
      // If userInfo.role is an array, check if any of the user's roles match the required roles
      if (Array.isArray(userInfo.role)) {
        const hasRequiredRole = requiredRoles.some((role) =>
          userInfo.role.includes(role),
        );

        if (hasRequiredRole) {
          return true;
        }
      } else {
        // If userInfo.role is a string, check if it matches any of the required roles
        const hasRequiredRole = requiredRoles.includes(userInfo.role);

        if (hasRequiredRole) {
          return true;
        }
      }
    }

    // If the user doesn't have the required role, redirect to an error page
    this.router.navigate(['/error'], {
      state: {
        message: 'You do not have permission to access this resource.',
      },
    });

    return false;
  }
}
