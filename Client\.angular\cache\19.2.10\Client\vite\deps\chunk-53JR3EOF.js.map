{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-autofocus.mjs"], "sourcesContent": ["import { DOCUMENT, isPlatform<PERSON>rowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, ElementRef, booleanAttribute, Input, Directive, NgModule } from '@angular/core';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { DomHandler } from 'primeng/dom';\n\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\nclass AutoFocus extends BaseComponent {\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @deprecated use [pAutoFocus]=\"true\"\n   * @group Props\n   */\n  autofocus = false;\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @group Props\n   */\n  _autofocus = false;\n  focused = false;\n  platformId = inject(PLATFORM_ID);\n  document = inject(DOCUMENT);\n  host = inject(ElementRef);\n  ngAfterContentChecked() {\n    // This sets the `attr.autofocus` which is different than the Input `autofocus` attribute.\n    if (this.autofocus === false) {\n      this.host.nativeElement.removeAttribute('autofocus');\n    } else {\n      this.host.nativeElement.setAttribute('autofocus', true);\n    }\n    if (!this.focused) {\n      this.autoFocus();\n    }\n  }\n  ngAfterViewChecked() {\n    if (!this.focused) {\n      this.autoFocus();\n    }\n  }\n  autoFocus() {\n    if (isPlatformBrowser(this.platformId) && this._autofocus) {\n      setTimeout(() => {\n        const focusableElements = DomHandler.getFocusableElements(this.host?.nativeElement);\n        if (focusableElements.length === 0) {\n          this.host.nativeElement.focus();\n        }\n        if (focusableElements.length > 0) {\n          focusableElements[0].focus();\n        }\n        this.focused = true;\n      });\n    }\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵAutoFocus_BaseFactory;\n    return function AutoFocus_Factory(__ngFactoryType__) {\n      return (ɵAutoFocus_BaseFactory || (ɵAutoFocus_BaseFactory = i0.ɵɵgetInheritedFactory(AutoFocus)))(__ngFactoryType__ || AutoFocus);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: AutoFocus,\n    selectors: [[\"\", \"pAutoFocus\", \"\"]],\n    inputs: {\n      autofocus: [2, \"autofocus\", \"autofocus\", booleanAttribute],\n      _autofocus: [0, \"pAutoFocus\", \"_autofocus\"]\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[pAutoFocus]',\n      standalone: true\n    }]\n  }], null, {\n    autofocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    _autofocus: [{\n      type: Input,\n      args: ['pAutoFocus']\n    }]\n  });\n})();\nclass AutoFocusModule {\n  static ɵfac = function AutoFocusModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AutoFocusModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: AutoFocusModule,\n    imports: [AutoFocus],\n    exports: [AutoFocus]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoFocusModule, [{\n    type: NgModule,\n    args: [{\n      imports: [AutoFocus],\n      exports: [AutoFocus]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoFocus, AutoFocusModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,YAAN,MAAM,mBAAkB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpC,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,aAAa;AAAA,EACb,UAAU;AAAA,EACV,aAAa,OAAO,WAAW;AAAA,EAC/B,WAAW,OAAO,QAAQ;AAAA,EAC1B,OAAO,OAAO,UAAU;AAAA,EACxB,wBAAwB;AAEtB,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,KAAK,cAAc,gBAAgB,WAAW;AAAA,IACrD,OAAO;AACL,WAAK,KAAK,cAAc,aAAa,aAAa,IAAI;AAAA,IACxD;AACA,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,kBAAkB,KAAK,UAAU,KAAK,KAAK,YAAY;AACzD,iBAAW,MAAM;AACf,cAAM,oBAAoB,WAAW,qBAAqB,KAAK,MAAM,aAAa;AAClF,YAAI,kBAAkB,WAAW,GAAG;AAClC,eAAK,KAAK,cAAc,MAAM;AAAA,QAChC;AACA,YAAI,kBAAkB,SAAS,GAAG;AAChC,4BAAkB,CAAC,EAAE,MAAM;AAAA,QAC7B;AACA,aAAK,UAAU;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kBAAkB,mBAAmB;AACnD,cAAQ,2BAA2B,yBAA4B,sBAAsB,UAAS,IAAI,qBAAqB,UAAS;AAAA,IAClI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IAClC,QAAQ;AAAA,MACN,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,MACzD,YAAY,CAAC,GAAG,cAAc,YAAY;AAAA,IAC5C;AAAA,IACA,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,SAAS;AAAA,IACnB,SAAS,CAAC,SAAS;AAAA,EACrB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,SAAS;AAAA,MACnB,SAAS,CAAC,SAAS;AAAA,IACrB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}