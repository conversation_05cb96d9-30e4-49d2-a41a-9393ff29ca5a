# Stage 1: Build the .NET application
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build

# Set working directory
WORKDIR /src

# Copy solution and project files
COPY ["Server/Server.sln", "Server/"]
COPY ["Server/Server.csproj", "Server/"]
COPY ["Server.Core/Server.Core.csproj", "Server.Core/"]
COPY ["Server.EntityFramworkCore/Server.EntityFramworkCore.csproj", "Server.EntityFramworkCore/"]
COPY ["Server.Services/Server.Services.csproj", "Server.Services/"]

# Restore dependencies
RUN dotnet restore "Server/Server.csproj"

# Copy the rest of the application code
COPY ["Server/", "Server/"]
COPY ["Server.Core/", "Server.Core/"]
COPY ["Server.EntityFramworkCore/", "Server.EntityFramworkCore/"]
COPY ["Server.Services/", "Server.Services/"]

# Build the application
RUN dotnet build "Server/Server.csproj" -c Release -o /app/build

# Stage 2: Publish the application
FROM build AS publish
RUN dotnet publish "Server/Server.csproj" -c Release -o /app/publish

# Stage 3: Create the runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final

# Set working directory
WORKDIR /app

# Copy the published application
COPY --from=publish /app/publish .

# Create directory for static files
RUN mkdir -p /app/wwwroot

# Expose port
EXPOSE 5001

# Set environment variables
ENV ASPNETCORE_URLS=http://+:5001
ENV ASPNETCORE_ENVIRONMENT=Docker

# Start the application
ENTRYPOINT ["dotnet", "Server.dll"]
