﻿﻿using Server.Core.Entities.Events.EventContactDetailsModel;
using Server.Core.Entities.Events.EventLocationModel;
using Server.Core.Entities.UserManagement.UserModel;

namespace Server.Core.Entities.Events.EventModel {
    public class EventResponseDto {

        public int Id { get; set; }

        // Basic Info
        public string Title { get; set; }

        public string TypeName { get; set; }

        public string Category { get; set; }

        public int? Capacity { get; set; }

        public string Description { get; set; }

        public string EventImageUrl { get; set; }

        public bool RequiresRegistration { get; set; }

        // Date and Time
        public DateTime EventStarts { get; set; }

        public DateTime EventEnds { get; set; }

        public TimeSpan StartTime { get; set; }

        public TimeSpan EndTime { get; set; }

        public bool DisplayStartTime { get; set; }

        public bool DisplayEndTime { get; set; }

        // Location Type (Venue or Online)
        public EventLocationType LocationType { get; set; }
        public string LocationTypeName { get; set; }

        // Status and Approval
        public string StatusName { get; set; }
        public bool IsApproved { get; set; }
        public DateTime? SubmittedOn { get; set; }
        public DateTime? ReviewedOn { get; set; }
        public string? ReviewedById { get; set; }
        public string? ReviewedByName { get; set; }
        public string? RejectionReason { get; set; }

        // Submitter Information
        public string SubmitterName { get; set; }

        // Organizer Information
        public string OrganizerId { get; set; }
        public string OrganizerName { get; set; }

        // Timestamps
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public EventLocationDto Location { get; set; }
        public EventContactDetailsDto ContactDetails { get; set; }
    }
}
