export enum ResourceType {
  ExternalPartner = 1,
  SouthWardPromiseNeighbourhood = 2,
  SWPNPartner = 3,
}

export interface ContactDetails {
  id: number;
  contactName: string;
  contactNo: string;
  email: string;
  website: string;
  resourceId: number;
}

export interface SocialMedia {
  id: number;
  facebook: string;
  twitter: string;
  instagram: string;
  linkedIn: string;
  resourceId: number;
}

export interface Resource {
  id: number;
  organizationTitle: string;
  subTitle: string;
  resourceCategory: string;
  resourceImageUrl: string;
  resourceLogoUrl: string;
  resourceImagePath?: string;
  resourceLogoPath?: string;

  // Address
  address: string;
  city: string;
  state: string;
  zipCode: string;

  // Description
  shortDescription: string;
  longDescription: string;

  // Resource Type
  type?: ResourceType;
  resourceTypeName?: string;

  // Timestamps
  createdAt: Date;
  updatedAt?: Date;

  // Navigation properties
  services: string[];
  contactDetails: ContactDetails;
  socialMedia: SocialMedia;

  // Additional properties
  fullAddress?: string;
}

export interface ResourceRequest {
  id?: number; // Only for updates
  organizationTitle: string;
  subTitle?: string;
  resourceCategory: string;
  resourceImage?: File;
  resourceLogo?: File;
  resourceImagePath?: string;
  resourceLogoPath?: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  shortDescription: string;
  longDescription: string;
  type: ResourceType;
  services: string[];
  contactDetails: ContactDetails;
  socialMedia: SocialMedia;
}

export interface ApiResponse<T = any> {
  success?: boolean;
  isSuccess?: boolean; // Server uses IsSuccess
  message?: string;
  data?: T;
  errors?: any;
}
