# FullStackSFL - Dockerized

This repository contains a full stack application with an Angular frontend and ASP.NET Core backend, containerized using Docker.

## Prerequisites

- [Docker](https://www.docker.com/get-started)
- [Docker Compose](https://docs.docker.com/compose/install/)

## Getting Started

### 1. Environment Setup

Create a `.env` file in the root directory with your AWS credentials:

```
AWS_ACCESS_KEY=your_aws_access_key
AWS_SECRET_KEY=your_aws_secret_key
```

### 2. Build and Run

To build and run the application:

```bash
docker-compose up -d
```

This will:

- Build the Angular frontend
- Build the ASP.NET Core backend
- Start a SQL Server instance
- Set up networking between the containers

### 3. Access the Application

- Frontend: http://localhost:4200
- Backend API: http://localhost:5020
- Swagger UI: http://localhost:5020/swagger

### 4. Stop the Application

To stop the application:

```bash
docker-compose down
```

To stop the application and remove volumes:

```bash
docker-compose down -v
```

## Container Structure

- **client**: Angular frontend served with Nginx
- **server**: ASP.NET Core backend API
- **db**: SQL Server database

## Volumes

- **sqlserver-data**: Persists SQL Server database files
- **server-data**: Persists uploaded files in the wwwroot directory

## Notes

- The SQL Server container uses the password `YourStrong!Passw0rd`. Change this in production.
- AWS credentials are stored in the `.env` file, which should not be committed to version control in a production environment.
- The first time you run the application, the database will be empty. The application will create the necessary tables on startup.

## Troubleshooting

### Checking Container Status

Use the provided script to check the status of all containers:

```powershell
.\docker-check.ps1
```

This will check if all containers are running and if the frontend and backend are accessible.

### Database Connection Issues

If the backend cannot connect to the database, ensure the SQL Server container is running:

```bash
docker-compose ps
```

You may need to wait a few moments for SQL Server to initialize fully.

### Port Conflicts

If you have port conflicts, you can modify the port mappings in the `docker-compose.yml` file.

### Viewing Logs

To view container logs:

```bash
docker-compose logs
```

For a specific container:

```bash
docker-compose logs frontend
docker-compose logs server
docker-compose logs db
```

### File Permissions

If you encounter file permission issues with volumes, you may need to adjust permissions:

```bash
docker-compose down
sudo chown -R $USER:$USER ./data
docker-compose up -d
```

### Restarting Containers

To restart all containers:

```bash
docker-compose restart
```

To restart a specific container:

```bash
docker-compose restart frontend
docker-compose restart server
docker-compose restart db
```
