﻿﻿using AutoMapper;
using Server.Core.Entities.Events.EventContactDetailsModel;
using Server.Core.Entities.Events.EventLocationModel;
using Server.Core.Entities.Events.EventModel;
using Server.Core.Entities.UserManagement.UserModel;

namespace Server.Core.MappingProfile {
    public class EventMappingProfile : Profile {

        public EventMappingProfile() {

            // Event mapping
            CreateMap<EventCreateDto, Event>()
                .ForMember(dest => dest.EventImagePath, opt => opt.MapFrom(src => src.EventImagePath))
                .ForMember(dest => dest.Location, opt => opt.Ignore())
                .ForMember(dest => dest.ContactDetails, opt => opt.Ignore())
                .ForMember(dest => dest.IsApproved, opt => opt.MapFrom(src => src.SkipApproval))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.SubmittedOn, opt => opt.MapFrom(src => src.SubmittedOn))
                .ForMember(dest => dest.SubmitterName, opt => opt.MapFrom(src => src.SubmitterName))
                .ForMember(dest => dest.OrganizerId, opt => opt.MapFrom(src => src.OrganizerId));

            CreateMap<EventUpdateDto, Event>()
                .ForMember(dest => dest.EventImagePath, opt => opt.MapFrom(src =>
                    string.IsNullOrEmpty(src.EventImagePath) ? null : src.EventImagePath))
                .ForMember(dest => dest.Location, opt => opt.Ignore())
                .ForMember(dest => dest.ContactDetails, opt => opt.Ignore())
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.SubmittedOn, opt => opt.MapFrom(src => src.SubmittedOn))
                .ForMember(dest => dest.ReviewedOn, opt => opt.MapFrom(src => src.ReviewedOn))
                .ForMember(dest => dest.OrganizerId, opt => opt.MapFrom(src => src.OrganizerId));

            // Event response mapping
            CreateMap<Event, EventResponseDto>()
                .ForMember(dest => dest.TypeName, opt => opt.MapFrom(src => src.Type.ToString()))
                .ForMember(dest => dest.LocationType, opt => opt.MapFrom(src => src.LocationType))
                .ForMember(dest => dest.LocationTypeName, opt => opt.MapFrom(src => src.LocationType.ToString()))
                .ForMember(dest => dest.StatusName, opt => opt.MapFrom(src => src.Status.ToString()))
                .ForMember(dest => dest.EventImageUrl, opt => opt.Ignore())
                .ForMember(dest => dest.OrganizerId, opt => opt.MapFrom(src => src.OrganizerId))
                .ForMember(dest => dest.SubmitterName, opt => opt.MapFrom(src =>
                    !string.IsNullOrEmpty(src.SubmitterName) ? src.SubmitterName :
                    (src.Organizer != null ? $"{src.Organizer.FullName}" : string.Empty)))
                .ForMember(dest => dest.OrganizerName, opt => opt.MapFrom(src =>
                    src.Organizer != null ? $"{src.Organizer.FullName}" : string.Empty))
                .ForMember(dest => dest.ReviewedById, opt => opt.MapFrom(src => src.ReviewedById))
                .ForMember(dest => dest.ReviewedByName, opt => opt.MapFrom(src =>
                    src.ReviewedBy != null ? $"{src.ReviewedBy.FullName}" : "Admin"));

            // EventLocation mapping
            CreateMap<EventLocationDto, EventLocation>();
            CreateMap<EventLocation, EventLocationDto>();

            // EventContactDetails mapping
            CreateMap<EventContactDetailsDto, EventContactDetails>();
            CreateMap<EventContactDetails, EventContactDetailsDto>();
        }
    }
}

