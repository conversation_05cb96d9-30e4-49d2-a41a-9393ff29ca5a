<link
  href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"
  rel="stylesheet"
/>
<link
  href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css"
  rel="stylesheet"
/>

<div *ngIf="showHeader">
  <nav class="navbar navbar-expand-lg navbar-light">
    <div class="container-fluid" (click)="closeNotifications($event)">
      <!-- Logo Section -->
      <a class="navbar-brand d-flex align-items-center me-auto" href="#">
        <img
          src="https://cdn.worldvectorlogo.com/logos/stocard-wordmark-1.svg"
          alt="Logo"
          style="height: 50px; width: 150px"
          class="me-2"
        />
      </a>

      <!-- Navigation Links -->
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a
              class="nav-link"
              routerLinkActive="active"
              routerLink="/dashboard"
              >Dashboard</a
            >
          </li>
          <li class="nav-item">
            <a class="nav-link" routerLinkActive="active" routerLink="/events"
              >Events</a
            >
          </li>
          <li class="nav-item">
            <a
              class="nav-link"
              routerLinkActive="active"
              routerLink="/resources"
              >Resources</a
            >
          </li>
          <li class="nav-item">
            <a class="nav-link" routerLinkActive="active" routerLink="/users"
              >User Management</a
            >
          </li>
        </ul>
      </div>

      <!-- Icons Section -->
      <div class="d-flex align-items-center">
        <!-- Notification Bell Button -->
        <div class="position-relative me-3">
          <button
            class="btn btn-link p-0 bell-button notification-bell"
            (click)="toggleNotifications()"
          >
            <i class="bi bi-bell"></i>
            <span
              *ngIf="unreadCount > 0"
              class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
            >
              {{ unreadCount > 9 ? "9+" : unreadCount }}
              <span class="visually-hidden">unread notifications</span>
            </span>
          </button>

          <!-- Notification Panel -->
          <div
            *ngIf="showNotifications"
            class="notification-panel card shadow-lg"
          >
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h5 class="mb-0">Notifications</h5>
              <!-- Clear All Button - only show when there are notifications -->
              <button
                *ngIf="hasNotifications()"
                class="btn btn-outline-secondary btn-sm clear-all-btn"
                (click)="clearAllNotifications()"
                title="Clear all notifications"
                type="button"
              >
                <i class="bi bi-trash me-1"></i>
                Clear All
              </button>
            </div>

            <div class="card-body p-0">
              <!-- Loading Spinner -->
              <div
                *ngIf="isLoadingNotifications"
                class="d-flex justify-content-center p-3"
              >
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>

              <!-- No Notifications Message -->
              <div
                *ngIf="
                  !isLoadingNotifications && notificationGroups.length === 0
                "
                class="p-3 text-center text-muted"
              >
                <i class="bi bi-bell-slash fs-4 d-block mb-2"></i>
                <p>No notifications to display</p>
              </div>

              <!-- Notification Items Grouped by Date -->
              <div
                *ngIf="!isLoadingNotifications && notificationGroups.length > 0"
                class="notification-list"
              >
                <!-- Loop through each date group -->
                <div
                  *ngFor="let group of notificationGroups"
                  class="notification-group"
                >
                  <!-- Date Group Header -->
                  <div class="notification-group-header">
                    <h6 class="mb-0">{{ group.title }}</h6>
                  </div>

                  <!-- Notifications in this group -->
                  <div
                    *ngFor="let notification of group.notifications"
                    class="notification-item"
                    (click)="
                      navigateToEvent(notification.eventId, notification.id)
                    "
                  >
                    <!-- Red dot - only show if notification is not read -->
                    <div
                      class="notification-dot"
                      *ngIf="!notification.isRead"
                    ></div>
                    <!-- Empty space to maintain alignment when dot is hidden -->
                    <div
                      class="notification-dot-placeholder"
                      *ngIf="notification.isRead"
                    ></div>
                    <div class="notification-content">
                      <div>
                        <a class="event-link">{{ notification.title }}</a>
                        {{ notification.message }}
                        <a
                          class="organizer-link"
                          *ngIf="notification.status !== 'Event Has Started'"
                          >{{ notification.submitterName }}</a
                        ><span
                          *ngIf="notification.status !== 'Event Has Started'"
                          >,</span
                        >
                        <span>
                          {{ notification.startDate | date: "dd MMM yyyy" }}
                          {{
                            notification.displayStartTime &&
                            notification.startTime
                              ? " - " +
                                (notification.startTime | slice: 0 : 5) +
                                "pm"
                              : ""
                          }}
                          {{
                            notification.displayEndTime
                              ? " | " +
                                (notification.endDate | date: "dd MMM yyyy") +
                                " - " +
                                (notification.endTime | slice: 0 : 5) +
                                "pm"
                              : ""
                          }}
                        </span>
                      </div>
                      <div class="notification-time">
                        {{ notification.timeAgo }}
                      </div>
                    </div>

                    <!-- Delete button -->
                    <button
                      class="notification-delete-btn"
                      (click)="deleteNotification($event, notification)"
                      title="Delete notification"
                      type="button"
                    >
                      <i class="bi bi-x"></i>
                    </button>

                    <div
                      class="notification-status"
                      [ngClass]="{
                        'pending-review':
                          notification.status === 'Pending Review',
                        approved: notification.status === 'Approved',
                        'event-started':
                          notification.status === 'Event Has Started',
                        rejected: notification.status === 'Rejected',
                      }"
                    >
                      {{ notification.status }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Confirmation Dialog for notification deletion -->
        <p-confirmDialog
          key="deleteNotification"
          styleClass="notification-delete-confirmation"
          [style]="{ width: '450px' }"
          [baseZIndex]="10000"
          [autoZIndex]="true"
        >
        </p-confirmDialog>

        <!-- Confirmation Dialog for clear all notifications -->
        <p-confirmDialog
          key="clearAllNotifications"
          styleClass="notification-clear-all-confirmation"
          [style]="{ width: '450px' }"
          [baseZIndex]="10000"
          [autoZIndex]="true"
        >
        </p-confirmDialog>

        <!-- User Avatar Dropdown -->
        <div class="dropdown">
          <img
            [src]="userAvatar"
            id="user-avatar"
            alt="User Avatar"
            class="rounded-circle dropdown-toggle"
            style="height: 40px; width: 40px; cursor: pointer"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          />
          <ul
            class="dropdown-menu dropdown-menu-end p-0"
            aria-labelledby="user-avatar"
            style="min-width: 260px"
          >
            <li
              class="px-3 py-3 border-bottom d-flex align-items-center"
              style="background: #f8f9fa"
            >
              <img
                [src]="userAvatar"
                alt="User Avatar"
                class="rounded-circle me-2"
                style="height: 48px; width: 48px"
              />
              <div>
                <div class="fw-semibold">{{ userName }}</div>
                <div class="text-muted small">{{ userEmail }}</div>
              </div>
            </li>
            <li>
              <a class="dropdown-item" routerLink="/profile"
                ><i class="bi bi-person me-2"></i>Profile</a
              >
            </li>
            <li>
              <a class="dropdown-item" routerLink="/change-password">
                <i class="bi bi-lock me-2"></i>Change Password
              </a>
            </li>
            <li><hr class="dropdown-divider" /></li>
            <li class="logout-btn">
              <a class="dropdown-item" (click)="logout()"
                ><i class="bi bi-box-arrow-right me-2"></i>Logout</a
              >
            </li>
          </ul>
        </div>
      </div>
    </div>
  </nav>
</div>
