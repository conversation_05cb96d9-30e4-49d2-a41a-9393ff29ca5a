# PowerShell script to stop the Docker containers

Write-Host "Stopping FullStackSFL Docker containers..." -ForegroundColor Yellow

# Stop the containers
docker-compose down

if ($LASTEXITCODE -eq 0) {
    Write-Host "Containers stopped successfully!" -ForegroundColor Green
} else {
    Write-Host "Error stopping containers. Please check if <PERSON><PERSON> is running." -ForegroundColor Red
}

# Ask if user wants to remove volumes
$removeVolumes = Read-Host "Do you want to remove volumes as well? (y/n)"
if ($removeVolumes -eq "y" -or $removeVolumes -eq "Y") {
    Write-Host "Removing volumes..." -ForegroundColor Yellow
    docker-compose down -v
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Volumes removed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Error removing volumes." -ForegroundColor Red
    }
}
