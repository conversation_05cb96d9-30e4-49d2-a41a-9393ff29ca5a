﻿﻿using Server.Core.Entities.Events.EventModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Server.Core.Entities.Events.EventContactDetailsModel {
    public class EventContactDetails {

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        
        public int EventId { get; set; }
        
        public string ContactName { get; set; }
        
        public string ContactNo { get; set; }
        
        public string Website { get; set; }
        
        public string Email { get; set; }

        // Navigation property
        public virtual Event Event { get; set; }
    }
}
