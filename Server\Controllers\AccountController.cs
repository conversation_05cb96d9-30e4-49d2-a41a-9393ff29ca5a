﻿
using Microsoft.AspNetCore.Authorization;

using Microsoft.AspNetCore.Mvc;
using Server.Core.Entities.UserManagement.ChangePasswordModel;
using Server.Core.Entities.UserManagement.LoginModel;
using Server.Core.Entities.UserManagement.RegisterModel;
using Server.Core.Entities.UserManagement.ResendOtpRequestModel;
using Server.Core.Entities.UserManagement.ResponseModel;
using Server.Core.Entities.UserManagement.ToggleStatusRequestModel;
using Server.Core.Entities.UserManagement.UserDetailsModel;
using Server.Core.Entities.UserManagement.VerifyOtpRequestModel;
using Server.Core.Pagination.PagedResponseModel;
using Server.Core.Pagination.PaginationParametersModel;
using Server.Services.AccountServices;

using System.Security.Claims;


namespace Server.Controllers {
    //[Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AccountController : ControllerBase {
        private readonly IAccountService _accountService;

        public AccountController(IAccountService accountService) {
            _accountService = accountService;
        }

        [HttpPost("AddUser")]
        [AllowAnonymous]
        public async Task<ActionResult<Response>> Register(Register register) {
            var creatorId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(creatorId)) {
                return Unauthorized(new Response {
                    IsSuccess = false,
                    Message = "Unauthorized: Unable to determine the creator"
                });
            }

            var response = await _accountService.RegisterUserAsync(register, creatorId);
            return Ok(response);
        }

        [HttpPut("UpdateUser/{userId}")]

        public async Task<IActionResult> UpdateUser(string userId, [FromBody] UserDetails userDetails) {
            var response = await _accountService.UpdateUserAsync(userId, userDetails);
            return Ok(response);
        }

        [HttpDelete("DeleteUser/{userId}")]

        public async Task<IActionResult> DeleteUser(string userId) {
            var response = await _accountService.DeleteUserAsync(userId);
            return Ok(response);
        }

        [HttpPost("Login")]
        [AllowAnonymous]
        public async Task<ActionResult<Response>> Login(Login login) {
            var response = await _accountService.LoginAsync(login);
            return Ok(response);
        }

        [HttpPost("VerifyOtp")]
        [AllowAnonymous]
        public async Task<IActionResult> VerifyOtp(VerifyOtpRequest request) {
            var response = await _accountService.VerifyOtpAsync(request);
            return Ok(response);
        }

        [HttpPost("ResendOtp")]
        [AllowAnonymous]
        public async Task<IActionResult> ResendOtp([FromBody] ResendOtpRequest request) {
            var response = await _accountService.ResendOtpAsync(request.UserId);
            return Ok(response);
        }

        [HttpGet("GetUserById/{userId}")]
        public async Task<IActionResult> GetUserById(string userId) {
            var userDetails = await _accountService.GetUserByIdAsync(userId);
            if (userDetails == null) {
                return NotFound(new Response {
                    IsSuccess = false,
                    Message = "User not found"
                });
            }

            return Ok(userDetails);
        }


        [HttpGet("GetUsers")]
        public async Task<ActionResult<PagedResponse<UserDetails>>> GetPagedUsers([FromQuery] PaginationParameters parameters) {
            try {
                var pagedResponse = await _accountService.GetPagedUsersAsync(parameters);
                return Ok(pagedResponse);
            }
            catch (Exception ex) {
                // Log the exception
                return StatusCode(500, new Response {
                    IsSuccess = false,
                    Message = "An error occurred while retrieving users",
                    Data = ex.Message
                });
            }
        }


        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAllUsers() {
            var users = await _accountService.GetAllUsersAsync();
            return Ok(users);
        }

        [HttpPut("ToggleStatus/{id}")]
        public async Task<IActionResult> ToggleStatus(string id, [FromBody] ToggleStatusRequest request) {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(currentUserId)) {
                return Unauthorized(new Response {
                    IsSuccess = false,
                    Message = "Unauthorized: Unable to determine the current user"
                });
            }

            var response = await _accountService.ToggleUserStatusAsync(id, request, currentUserId);
            if (!response.IsSuccess) {
                return BadRequest(response);
            }

            return Ok(response);
        }

        [HttpPost("ChangePassword")]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request) {
            if (!ModelState.IsValid) {
                return BadRequest(new Response {
                    IsSuccess = false,
                    Message = "Invalid request",
                    Data = ModelState
                });
            }

            var response = await _accountService.ChangePasswordAsync(request);
            if (!response.IsSuccess) {
                return BadRequest(response);
            }

            return Ok(response);
        }
    }





}

