﻿namespace Server.Services.OtpServices {
    public class OtpServiceDummy {

        // This is a dummy implementation of the IOtpService interface for testing purposes.
        // In a real-world scenario, this would interact with a database or other persistent storage.
        // This dummy implementation uses in-memory storage for demonstration purposes.

        private readonly Dictionary<string, (string Otp, DateTime Expiry)> _otpStore = new();
        private readonly Dictionary<string, DateTime> _verifiedTimestamps = new();

        public Task StoreOtp(string userId, string otp) {
            Console.WriteLine($"Storing OTP: {otp} for User: {userId} with Expiry: {DateTime.UtcNow.AddMinutes(5)}");
            _otpStore[userId] = (otp, DateTime.UtcNow.AddMinutes(10));
            return Task.CompletedTask;
        }

        public Task<bool> VerifyOtp(string userId, string otp) {
            if (_otpStore.TryGetValue(userId, out var record)) {
                Console.WriteLine($"Verifying OTP: {otp} for User: {userId}. Stored OTP: {record.Otp}, Expiry: {record.Expiry}");
                if (record.Otp == otp && record.Expiry > DateTime.UtcNow) {
                    return Task.FromResult(true);
                }
                Console.WriteLine("OTP is either invalid or expired.");
            }
            else {
                Console.WriteLine("No OTP found for the given User ID.");
            }
            return Task.FromResult(false);
        }

        public Task MarkOtpVerified(string userId) {
            Console.WriteLine($"Marking OTP as verified for User: {userId}");
            _verifiedTimestamps[userId] = DateTime.UtcNow;
            _otpStore.Remove(userId);
            return Task.CompletedTask;
        }

        public Task<bool> IsOtpRecentlyVerified(string userId) {
            return Task.FromResult(
        _verifiedTimestamps.TryGetValue(userId, out var lastVerified) &&
        DateTime.UtcNow - lastVerified < TimeSpan.FromHours(4)
    );
        }
    }
}
