import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class DateUtilsService {
  // IST offset is UTC+5:30
  private readonly IST_OFFSET_HOURS = 5;
  private readonly IST_OFFSET_MINUTES = 30;

  constructor() {}

  convertUtcToIst(utcDate: Date | string): Date {
    if (!utcDate) return new Date();

    const date = new Date(utcDate);

    // Create a new date by adding the IST offset
    const istDate = new Date(date.getTime());
    istDate.setHours(date.getHours() + this.IST_OFFSET_HOURS);
    istDate.setMinutes(date.getMinutes() + this.IST_OFFSET_MINUTES);

    return istDate;
  }

  formatDateForDisplay(date: Date | string): string {
    if (!date) return '';

    const istDate = this.convertUtcToIst(date);

    const day = istDate.getDate();
    const month = istDate.toLocaleString('default', { month: 'short' });
    const year = istDate.getFullYear();
    const hours = istDate.getHours();
    const minutes = istDate.getMinutes();

    // Convert to 12-hour format with AM/PM
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM

    // Format exactly like "14 Sep 2023, 5:30 PM"
    return `${day} ${month} ${year}, ${hours12}:${minutes < 10 ? '0' + minutes : minutes} ${ampm}`;
  }

  formatDateForBackend(date: Date): string {
    if (!date) return '';

    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');

    return `${year}-${month}-${day}T00:00:00`;
  }

  // Convert IST date to UTC for backend filtering
  // This ensures that when user selects a date in IST, it's properly converted to UTC for backend processing
  convertIstDateToUtcForFiltering(istDate: Date): Date {
    if (!istDate) return new Date();

    // Create a new date object to avoid modifying the original
    const utcDate = new Date(istDate.getTime());

    // Subtract IST offset to get the equivalent UTC date
    // IST is UTC+5:30, so we subtract 5 hours and 30 minutes
    utcDate.setHours(utcDate.getHours() - this.IST_OFFSET_HOURS);
    utcDate.setMinutes(utcDate.getMinutes() - this.IST_OFFSET_MINUTES);

    return utcDate;
  }

  getTimeAgo(date: Date | string): string {
    if (!date) return '';

    const now = new Date();
    const past = this.convertUtcToIst(date);
    const diffMs = now.getTime() - past.getTime();

    // Convert to seconds for more precise handling of recent events
    const diffSecs = Math.floor(diffMs / 1000);

    // Just now - for very recent events (less than 60 seconds)
    if (diffSecs < 60) return 'just now';

    // Minutes - for events in the last hour
    const diffMins = Math.floor(diffSecs / 60);
    if (diffMins === 1) return '1 min ago';
    if (diffMins < 60) return `${diffMins} mins ago`;

    // Hours - for events in the last day
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours === 1) return '1 hour ago';
    if (diffHours < 24) return `${diffHours} hours ago`;

    // Days - for events in the last week
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays === 1) return 'yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;

    // For older events, show the date
    return past.toLocaleDateString();
  }

  isSameDay(date1: Date, date2: Date): boolean {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  }
}
