<div class="profile-container">
  <div class="profile-header">
    <h2>Profile</h2>
    <div class="profile-actions">
      <!-- <button
        class="change-password-btn"
        (click)="changePassword()"
        [disabled]="isLoading"
      >
        <i class="bi bi-key"></i> Change Password
      </button> -->
      <button
        *ngIf="!isEditMode"
        class="edit-btn"
        (click)="toggleEditMode()"
        [disabled]="isLoading"
      >
        <i class="pi pi-pencil"></i> Edit
      </button>
      <div *ngIf="isEditMode" class="edit-actions">
        <button class="save-btn" (click)="onSubmit()" [disabled]="isSubmitting">
          <i class="pi pi-check"></i> Save
        </button>
        <button
          class="cancel-btn"
          (click)="cancelEdit()"
          [disabled]="isSubmitting"
        >
          <i class="pi pi-times"></i> Cancel
        </button>
      </div>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading || isSubmitting" class="loading-container">
    <p-progressSpinner
      [style]="{ width: '50px', height: '50px' }"
      strokeWidth="4"
      fill="transparent"
      animationDuration=".5s"
    ></p-progressSpinner>
    <p class="mt-2">
      {{ isSubmitting ? "Saving profile..." : "Loading profile data..." }}
    </p>
  </div>

  <!-- Validation Errors -->
  <div
    *ngIf="validationErrors.length > 0 && formSubmitted"
    class="validation-errors mb-3"
  >
    <div class="alert alert-danger">
      <h6 class="mb-2">Please fix the following errors:</h6>
      <ul class="mb-0">
        <li *ngFor="let error of validationErrors">{{ error }}</li>
      </ul>
    </div>
  </div>

  <!-- Error Messages -->
  <p-messages
    *ngIf="errorMessages.length > 0"
    [(value)]="errorMessages"
    [closable]="true"
    [style]="{ marginBottom: '1rem' }"
  ></p-messages>

  <!-- Profile Content -->
  <div class="profile-content" *ngIf="!isLoading && !isSubmitting">
    <div class="profile-avatar-container">
      <img [src]="userAvatar" alt="User Avatar" class="profile-avatar" />
    </div>

    <form
      [formGroup]="profileForm"
      (ngSubmit)="onSubmit()"
      class="profile-details compact-form"
    >
      <div class="profile-row">
        <div class="profile-field">
          <label
            >Name<span *ngIf="isEditMode" class="text-danger">*</span></label
          >
          <div *ngIf="!isEditMode" class="field-value">{{ userName }}</div>
          <div *ngIf="isEditMode">
            <input
              pInputText
              type="text"
              formControlName="fullName"
              class="w-100"
              placeholder="Enter your full name"
              [style]="{ height: '39px' }"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  profileForm.get('fullName')?.invalid &&
                  profileForm.get('fullName')?.touched,
              }"
            />
            <div
              *ngIf="
                profileForm.get('fullName')?.errors?.['required'] &&
                profileForm.get('fullName')?.touched
              "
              class="text-danger small mt-1"
            >
              Name is required
            </div>
          </div>
        </div>
        <div class="profile-field">
          <label>Email</label>
          <div class="field-value">{{ userEmail }}</div>
        </div>
        <div class="profile-field">
          <label>Profile Status</label>
          <div class="status-indicator">
            <span class="status-dot" [class.active]="isActive"></span>
            <span>{{ isActive ? "Active" : "Inactive" }}</span>
          </div>
        </div>
      </div>

      <div class="profile-row">
        <div class="profile-field">
          <label
            >Contact Number<span *ngIf="isEditMode" class="text-danger"
              >*</span
            ></label
          >
          <div *ngIf="!isEditMode" class="field-value">
            {{ phoneNumber || "Not provided" }}
          </div>
          <div *ngIf="isEditMode">
            <input
              pInputText
              type="tel"
              formControlName="phoneNumber"
              class="w-100"
              placeholder="10-digit phone number"
              pattern="^[0-9]{10}$"
              [style]="{ height: '39px' }"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  profileForm.get('phoneNumber')?.invalid &&
                  profileForm.get('phoneNumber')?.touched,
              }"
            />
            <div
              *ngIf="
                profileForm.get('phoneNumber')?.errors?.['required'] &&
                profileForm.get('phoneNumber')?.touched
              "
              class="text-danger small mt-1"
            >
              Phone Number is required
            </div>
            <div
              *ngIf="
                profileForm.get('phoneNumber')?.errors?.['pattern'] &&
                profileForm.get('phoneNumber')?.touched
              "
              class="text-danger small mt-1"
            >
              Phone Number must be 10 digits
            </div>
          </div>
        </div>
        <div class="profile-field">
          <label>User Role</label>
          <div class="field-value">{{ userRole }}</div>
        </div>
        <div class="profile-field">
          <label>Website</label>
          <div *ngIf="!isEditMode" class="field-value">
            {{ website || "Not provided" }}
          </div>
          <div *ngIf="isEditMode">
            <input
              pInputText
              type="url"
              formControlName="website"
              class="w-100"
              placeholder="https://example.com"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  profileForm.get('website')?.invalid &&
                  profileForm.get('website')?.touched,
              }"
            />
            <div
              *ngIf="
                profileForm.get('website')?.errors?.['pattern'] &&
                profileForm.get('website')?.touched
              "
              class="text-danger small mt-1"
            >
              Please enter a valid URL
            </div>
          </div>
        </div>
      </div>

      <div class="profile-row full-width">
        <div class="profile-field">
          <label>Description</label>
          <div *ngIf="!isEditMode" class="field-value description">
            {{ description || "No description available" }}
          </div>
          <div *ngIf="isEditMode">
            <textarea
              pInputTextarea
              formControlName="description"
              class="w-100"
              placeholder="Tell us about yourself..."
              rows="4"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  profileForm.get('description')?.invalid &&
                  profileForm.get('description')?.touched,
              }"
            ></textarea>
          </div>
        </div>
      </div>

      <h3 class="social-media-title">Social Media Links</h3>

      <div class="profile-row">
        <div class="profile-field">
          <label>Facebook</label>
          <div class="position-relative">
            <div
              class="position-absolute"
              style="
                top: 50%;
                transform: translateY(-50%);
                left: 10px;
                z-index: 1;
              "
            >
              <i class="pi pi-facebook" style="color: #4267b2"></i>
            </div>
            <input
              *ngIf="!isEditMode"
              type="text"
              [value]="facebookId || ''"
              class="form-control social-input"
              readonly
              style="padding-left: 35px; width: 100%"
            />
            <input
              *ngIf="isEditMode"
              pInputText
              type="text"
              formControlName="facebook"
              placeholder="www.facebook.com/username"
              pattern="(https?:\/\/)?(www\.)?facebook\.com\/[a-zA-Z0-9\.]+"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  profileForm.get('facebook')?.invalid &&
                  profileForm.get('facebook')?.touched,
              }"
              style="padding-left: 35px; width: 100%"
            />
          </div>
          <div
            *ngIf="
              isEditMode &&
              profileForm.get('facebook')?.errors?.['pattern'] &&
              profileForm.get('facebook')?.touched
            "
            class="text-danger small mt-1"
          >
            Please enter a valid Facebook URL
          </div>
        </div>
        <div class="profile-field">
          <label>Twitter (X)</label>
          <div class="position-relative">
            <div
              class="position-absolute"
              style="
                top: 50%;
                transform: translateY(-50%);
                left: 10px;
                z-index: 1;
              "
            >
              <i class="pi pi-twitter" style="color: #1da1f2"></i>
            </div>
            <input
              *ngIf="!isEditMode"
              type="text"
              [value]="twitterId || ''"
              class="form-control social-input"
              readonly
              style="padding-left: 35px; width: 100%"
            />
            <input
              *ngIf="isEditMode"
              pInputText
              type="text"
              formControlName="twitter"
              placeholder="www.twitter.com/username"
              pattern="(https?:\/\/)?(www\.)?(twitter\.com|x\.com)\/[A-Za-z0-9_]{1,15}"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  profileForm.get('twitter')?.invalid &&
                  profileForm.get('twitter')?.touched,
              }"
              style="padding-left: 35px; width: 100%"
            />
          </div>
          <div
            *ngIf="
              isEditMode &&
              profileForm.get('twitter')?.errors?.['pattern'] &&
              profileForm.get('twitter')?.touched
            "
            class="text-danger small mt-1"
          >
            Please enter a valid Twitter URL
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

<!-- Toast for notifications -->
<p-toast></p-toast>
