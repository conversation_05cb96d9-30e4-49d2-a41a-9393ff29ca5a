using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Server.Core.Entities.Resources.ResourceModel;
using Server.Core.Entities.UserManagement.ResponseModel;
using Server.Core.Pagination.PaginationParameterForResourceModel;
using Server.Services.ResourceServices;

namespace Server.API.Controllers {
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ResourcesController : ControllerBase {
        private readonly IResourceService _resourceService;

        public ResourcesController(IResourceService resourceService) {
            _resourceService = resourceService;
        }

        // GET: api/Resources
        [HttpGet("GetResources")]
        public async Task<IActionResult> GetAll([FromQuery] PaginationParameterEvents parameters) {
            var result = await _resourceService.GetAllResource(parameters);
            return Ok(result);
        }

        // GET: api/Resources/{id}
        [HttpGet("GetResources/{id}")]
        public async Task<IActionResult> GetById(int id) {
            var resource = await _resourceService.GetResourceById(id);
            if (resource == null) {

                return NotFound(new Response {
                    IsSuccess = false,
                    Message = "User not found"
                });
            }

            return Ok(resource);
        }

        // POST: api/Resources
        [HttpPost("AddResources")]
        public async Task<IActionResult> Create([FromForm] ResourceCreateDTO dto) {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            // Let the service handle file uploads - don't do it here
            // This prevents double file uploads

            var response = await _resourceService.CreateResource(dto);
            if (response.IsSuccess)
                return Ok(response);

            return BadRequest(response);
        }

        // PUT: api/Resources
        [HttpPut("UpdateResources")]
        public async Task<IActionResult> Update([FromForm] ResourceUpdateDto dto) {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            // Let the service handle file uploads - don't do it here
            // This prevents double file uploads

            var response = await _resourceService.UpdateResource(dto);
            if (response.IsSuccess)
                return Ok(response);

            return BadRequest(response);
        }

        // DELETE: api/Resources/{id}
        [HttpDelete("DeleteResources/{id}")]
        public async Task<IActionResult> Delete(int id) {

            // First get the resource to access its file paths
            var resource = await _resourceService.GetResourceById(id);
            if (resource == null) {
                return NotFound(new Response {
                    IsSuccess = false,
                    Message = "Resource not found"
                });
            }


            // Now delete the resource itself
            var response = await _resourceService.DeleteResource(id);
            if (response.IsSuccess)
                return Ok(response);

            return NotFound(response);
        }
    }
}
