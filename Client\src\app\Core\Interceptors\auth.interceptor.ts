import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AuthService } from '../Services/auth.service';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    private authService: AuthService,
    private router: Router,
    private messageService: MessageService,
  ) {}

  intercept(
    request: HttpRequest<any>,
    next: <PERSON>ttp<PERSON>and<PERSON>,
  ): Observable<HttpEvent<any>> {
    const token = this.authService.getToken();

    if (token) {
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`,
        },
      });
    }

    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          // Check if we're already on an auth route (login or OTP verification)
          const currentUrl = this.router.url;
          const isAuthRoute =
            currentUrl.includes('/authentication/login') ||
            currentUrl.includes('/authentication/verify-otp');

          // Only redirect to login if we're not already on an auth route
          if (!isAuthRoute) {
            this.authService.logout();

            // Show a toast message about the session expiry
            this.messageService.add({
              severity: 'error',
              summary: 'Session Expired',
              detail: 'Your session has expired. Please log in again.',
              life: 5000,
            });

            this.router.navigate(['/authentication/login'], {
              queryParams: { returnUrl: this.router.url },
            });
          }

          return throwError(
            () => new Error('Session expired. Please login again.'),
          );
        }

        if (error.status === 403) {
          this.router.navigate(['/error'], {
            state: {
              message: 'You do not have permission to access this resource.',
            },
          });
          return throwError(() => new Error('Access denied.'));
        }

        if (error.status === 0) {
          return throwError(
            () =>
              new Error(
                'Network error: Please check your connection and try again.',
              ),
          );
        }

        return throwError(() => error);
      }),
    );
  }
}
