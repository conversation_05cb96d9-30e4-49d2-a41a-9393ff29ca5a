import { Injectable } from '@angular/core';
import {
  CanActivate,
  CanLoad,
  Route,
  Router,
  UrlSegment,
} from '@angular/router';
import { AuthService } from '../Services/auth.service';

@Injectable({
  providedIn: 'root',
})
export class LoginGuard implements CanActivate, CanLoad {
  constructor(
    private authService: AuthService,
    private router: Router,
  ) {}

  canLoad(route: Route, segments: UrlSegment[]): boolean {
    try {
      if (this.authService.isLoggedIn()) {
        this.router.navigate(['/dashboard']);
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error in LoginGuard:', error);
      this.router.navigate(['/error']);
      return false;
    }
  }

  canActivate(): boolean {
    const mockRoute: Route = { path: '' };
    const mockSegments: UrlSegment[] = [];
    return this.canLoad(mockRoute, mockSegments);
  }
}
