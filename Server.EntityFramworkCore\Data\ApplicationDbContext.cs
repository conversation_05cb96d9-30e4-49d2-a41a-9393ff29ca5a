﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Server.Core.Entities.Events.EventContactDetailsModel;
using Server.Core.Entities.Events.EventLocationModel;
using Server.Core.Entities.Events.EventModel;
using Server.Core.Entities.Resources.ContactDetailsModel;
using Server.Core.Entities.Resources.ResourceModel;
using Server.Core.Entities.Resources.ServiceModel;
using Server.Core.Entities.Resources.SocialMediaModel;
using Server.Core.Entities.UserManagement.UserModel;
using Server.Core.Entities.UserManagement.UserOtpModel;

namespace Server.EntityFramworkCore.Data {
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser> {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options) {
        }

        public DbSet<UserOtp> UserOtps { get; set; }

        public DbSet<Resource> Resources { get; set; }
        public DbSet<Service> Services { get; set; }
        public DbSet<ContactDetails> ContactDetails { get; set; }
        public DbSet<SocialMedia> SocialMedia { get; set; }

        public DbSet<Event> Events { get; set; }
        public DbSet<EventLocation> EventLocations { get; set; }
        public DbSet<EventContactDetails> EventContactDetails { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder) {
            base.OnModelCreating(modelBuilder);

            // Configure Resource entity
            modelBuilder.Entity<Resource>(entity => {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id)
                .UseIdentityColumn();
                entity.Property(e => e.OrganizationTitle).IsRequired().HasMaxLength(200);
                entity.Property(e => e.SubTitle).HasMaxLength(200);
                entity.Property(e => e.ResourceCategory).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Address).IsRequired().HasMaxLength(200);
                entity.Property(e => e.City).IsRequired().HasMaxLength(100);
                entity.Property(e => e.State).IsRequired().HasMaxLength(50);
                entity.Property(e => e.ZipCode).IsRequired().HasMaxLength(20);
                entity.Property(e => e.ShortDescription).IsRequired();
                entity.Property(e => e.LongDescription).IsRequired();
            });

            // Configure Service entity
            modelBuilder.Entity<Service>(entity => {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id)
                .UseIdentityColumn();
                entity.Property(e => e.ServiceName).IsRequired().HasMaxLength(200);

                entity.HasOne(e => e.Resource)
                    .WithMany(r => r.Services)
                    .HasForeignKey(e => e.ResourceId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure ContactDetails entity
            modelBuilder.Entity<ContactDetails>(entity => {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id)
                .UseIdentityColumn();
                entity.Property(e => e.ContactName).HasMaxLength(100);
                entity.Property(e => e.ContactNo).HasMaxLength(20);
                entity.Property(e => e.Website).HasMaxLength(200);
                entity.Property(e => e.Email).HasMaxLength(100);

                entity.HasOne(e => e.Resource)
                    .WithOne(r => r.ContactDetails)
                    .HasForeignKey<ContactDetails>(e => e.ResourceId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure SocialMedia entity
            modelBuilder.Entity<SocialMedia>(entity => {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id)
                .UseIdentityColumn();
                entity.Property(e => e.Facebook).HasMaxLength(200);
                entity.Property(e => e.Instagram).HasMaxLength(200);
                entity.Property(e => e.Twitter).HasMaxLength(200);
                entity.Property(e => e.LinkedIn).HasMaxLength(200);

                entity.HasOne(e => e.Resource)
                    .WithOne(r => r.SocialMedia)
                    .HasForeignKey<SocialMedia>(e => e.ResourceId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure Event entity
            modelBuilder.Entity<Event>(entity => {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id)
                .UseIdentityColumn();
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Category).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).IsRequired();
            });

            // Configure EventLocation entity
            modelBuilder.Entity<EventLocation>(entity => {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id)
                .UseIdentityColumn();
                entity.Property(e => e.Address1).HasMaxLength(200);
                entity.Property(e => e.Address2).HasMaxLength(200);
                entity.Property(e => e.City).HasMaxLength(100);
                entity.Property(e => e.State).HasMaxLength(50);
                entity.Property(e => e.ZipCode).HasMaxLength(20);
                entity.Property(e => e.Country).HasMaxLength(100);
                entity.Property(e => e.MeetingId).HasMaxLength(100);
                entity.Property(e => e.Passcode).HasMaxLength(50);

                entity.HasOne(e => e.Event)
                    .WithOne(r => r.Location)
                    .HasForeignKey<EventLocation>(e => e.EventId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure EventContactDetails entity
            modelBuilder.Entity<EventContactDetails>(entity => {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id)
                .UseIdentityColumn();
                entity.Property(e => e.ContactName).HasMaxLength(100);
                entity.Property(e => e.ContactNo).HasMaxLength(20);
                entity.Property(e => e.Website).HasMaxLength(200);
                entity.Property(e => e.Email).HasMaxLength(100);

                entity.HasOne(e => e.Event)
                    .WithOne(r => r.ContactDetails)
                    .HasForeignKey<EventContactDetails>(e => e.EventId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

    }
}
