:host {
  display: block;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.otp-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  padding: 0;
  margin: 0;
  position: absolute;
  top: 0;
  left: 0;
}

.otp-card {
  width: 400px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.18);
  animation: fadeIn 0.5s ease-in-out;
  transition: all 0.3s ease;
}

.otp-header {
  text-align: center;
  margin-bottom: 30px;

  h1 {
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    margin-bottom: 10px;
  }

  .otp-validity-note {
    color: #4eff91;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 15px;
  }
}

.timer-container {
  margin-top: 20px;

  .p-progressbar {
    background: rgba(255, 255, 255, 0.2) !important;
    border: none !important;
    border-radius: 10px !important;
    overflow: hidden;

    .p-progressbar-value {
      background: linear-gradient(90deg, #11998e, #38ef7d) !important;
      border-radius: 10px !important;
    }

    &.time-warning .p-progressbar-value {
      background: linear-gradient(90deg, #ff9966, #ff5e62) !important;
    }
  }

  .timer-text {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;

    i {
      margin-right: 5px;
    }

    &.time-warning {
      color: #ff5e62;
    }
  }
}

.error-message {
  margin-bottom: 20px;

  ::ng-deep .p-message {
    width: 100%;
    background: rgba(255, 87, 87, 0.15);
    border-left: 3px solid #ff5757;
    border-radius: 8px;
    padding: 10px;

    .p-message-wrapper {
      padding: 0;
    }

    .p-message-icon {
      color: #ff5757;
    }

    .p-message-summary {
      color: white;
    }

    .p-message-detail {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.otp-input-container {
  margin: 25px 0;

  ::ng-deep .p-inputotp {
    display: flex;
    justify-content: center;
    gap: 12px;

    .p-inputtext {
      background: rgba(255, 255, 255, 0.15) !important;
      border: 2px solid rgba(255, 255, 255, 0.3) !important;
      border-radius: 12px !important;
      color: white !important;
      font-size: 1.5rem;
      width: 50px;
      height: 60px;
      text-align: center;
      transition: all 0.3s ease;

      &:focus {
        background: rgba(255, 255, 255, 0.25) !important;
        border-color: rgba(255, 255, 255, 0.5) !important;
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.2) !important;
      }
    }
  }

  .p-error {
    margin-top: 10px;
    color: #ff6b6b;
    font-size: 0.85rem;
    text-align: center;
  }
}

.form-actions-wrapper {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: relative;
  padding: 0 10px;
}

.buttons-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px; /* Increased gap between Verify and Resend OTP buttons */
}

.verify-button {
  background: #4eb5ff !important;
  border: none !important;
  border-radius: 8px !important;
  height: 45px;
  font-weight: 500;
  font-size: 1rem;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 10px rgba(78, 181, 255, 0.3);
  transition: all 0.3s ease !important;
  width: 140px !important; /* Wider for better appearance */

  &:hover:not(:disabled) {
    background: #3da8f5 !important;
    box-shadow: 0 6px 15px rgba(78, 181, 255, 0.4);
  }

  &:disabled {
    opacity: 0.7;
  }
}

.resend-container {
  display: flex;
  align-items: center;
  justify-content: center;

  .resend-button {
    color: #4eff91 !important;
    font-size: 0.9rem;
    background: rgba(78, 255, 145, 0.1) !important;
    border: none !important;
    border-radius: 20px !important;
    padding: 0.5rem 1rem !important;

    &:hover {
      color: white !important;
      background: rgba(255, 255, 255, 0.1) !important;
    }

    ::ng-deep .p-button-label {
      font-weight: normal;
    }

    ::ng-deep .p-button-icon {
      font-size: 0.9rem;
      margin-right: 5px;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
