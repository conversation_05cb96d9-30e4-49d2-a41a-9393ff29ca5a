import {
  Component,
  inject,
  OnI<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HostL<PERSON>ener,
  ElementRef,
} from '@angular/core';
import { AuthService } from '../../Core/Services/auth.service';
import { Router } from '@angular/router';
import { MessageService, ConfirmationService } from 'primeng/api';
import {
  NotificationService,
  NotificationGroup,
  Notification,
} from '../../Core/Services/notification.service';
import { UserAvatarService } from '../../Core/Services/user-avatar.service';
import { Subscription } from 'rxjs';

@Component({
  standalone: false,
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  providers: [ConfirmationService],
})
export class HeaderComponent implements OnInit, OnDestroy {
  userAvatar: string = '';
  userName: string = '';
  userEmail: string = '';
  userRole: string = '';
  showHeader: boolean = true;
  isGlobalAdmin: boolean = false;
  showNotifications: boolean = false;
  notificationGroups: NotificationGroup[] = [];
  notifications: Notification[] = [];
  unreadCount: number = 0;
  isLoadingNotifications: boolean = false;
  private avatarSubscription: Subscription | null = null;
  private notificationSubscriptions: Subscription[] = [];

  router = inject(Router);

  constructor(
    private authservice: AuthService,
    private notificationService: NotificationService,
    private messageService: MessageService,
    private userAvatarService: UserAvatarService,
    private confirmationService: ConfirmationService,
    private elementRef: ElementRef,
  ) {}

  ngOnInit(): void {
    this.avatarSubscription = this.userAvatarService
      .getUserAvatar()
      .subscribe((avatar) => {
        this.userAvatar = avatar;
      });

    const userInfo = this.authservice.getUserInfo();
    this.userName = userInfo?.name || '';
    this.userEmail = userInfo?.email || '';

    // Check if user has Global Admin role
    if (userInfo && userInfo.role) {
      this.userRole = Array.isArray(userInfo.role)
        ? userInfo.role[0]
        : userInfo.role;
      this.isGlobalAdmin = Array.isArray(userInfo.role)
        ? userInfo.role.includes('Global Admin')
        : userInfo.role === 'Global Admin';
    }

    this.router.events.subscribe(() => {
      this.showHeader =
        !this.router.url.includes('/authentication/login') &&
        !this.router.url.includes('/authentication/verify-otp');

      // Refresh user role on navigation
      if (this.authservice.isLoggedIn()) {
        const updatedUserInfo = this.authservice.getUserInfo();
        if (updatedUserInfo && updatedUserInfo.role) {
          this.userRole = Array.isArray(updatedUserInfo.role)
            ? updatedUserInfo.role[0]
            : updatedUserInfo.role;
          this.isGlobalAdmin = Array.isArray(updatedUserInfo.role)
            ? updatedUserInfo.role.includes('Global Admin')
            : updatedUserInfo.role === 'Global Admin';
        }
      }
    });

    // Subscribe to notification service observables with proper subscription management
    this.notificationSubscriptions.push(
      this.notificationService.getNotificationGroups().subscribe((groups) => {
        this.notificationGroups = groups;
      }),
    );

    this.notificationSubscriptions.push(
      this.notificationService.getNotifications().subscribe((notifications) => {
        this.notifications = notifications;
      }),
    );

    this.notificationSubscriptions.push(
      this.notificationService.getUnreadCount().subscribe((count) => {
        this.unreadCount = count;
      }),
    );

    this.notificationSubscriptions.push(
      this.notificationService.getIsLoading().subscribe((isLoading) => {
        this.isLoadingNotifications = isLoading;
      }),
    );

    // Initialize notification service to load notifications automatically
    // This will load the notification count immediately when the header loads
    if (this.authservice.isLoggedIn() && this.showHeader) {
      this.notificationService.initialize();
    }
  }

  toggleDropdown() {
    const dropdown = document.querySelector('.dropdown') as HTMLElement;
    dropdown.classList.toggle('active');
  }

  toggleNotifications() {
    // If panel is currently open and we're closing it, mark all visible notifications as read
    if (this.showNotifications) {
      this.markAllVisibleNotificationsAsRead();
    }

    this.showNotifications = !this.showNotifications;
  }

  // Mark all currently visible notifications as read when panel is closed
  private markAllVisibleNotificationsAsRead() {
    this.notificationService.markAllCurrentNotificationsAsRead();
  }

  // Close notification panel when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    if (this.showNotifications) {
      const target = event.target as HTMLElement;
      const notificationPanel = this.elementRef.nativeElement.querySelector(
        '.notification-panel',
      );
      const bellIcon =
        this.elementRef.nativeElement.querySelector('.notification-bell');

      // Check if click is outside notification panel and bell icon
      if (
        notificationPanel &&
        bellIcon &&
        !notificationPanel.contains(target) &&
        !bellIcon.contains(target)
      ) {
        // Mark all visible notifications as read before closing the panel
        this.markAllVisibleNotificationsAsRead();
        this.showNotifications = false;
      }
    }
  }

  closeNotifications(event: MouseEvent) {
    // Close notifications panel when clicking outside
    if (this.showNotifications) {
      const target = event.target as HTMLElement;
      const notificationPanel = document.querySelector(
        '.notification-panel',
      ) as HTMLElement;
      const bellButton = document.querySelector('.bell-button') as HTMLElement;

      if (
        notificationPanel &&
        !notificationPanel.contains(target) &&
        bellButton &&
        !bellButton.contains(target)
      ) {
        // Mark all visible notifications as read before closing the panel
        this.markAllVisibleNotificationsAsRead();
        this.showNotifications = false;
      }
    }
  }

  navigateToEvent(eventId: number, notificationId: number) {
    // Mark notification as read immediately for instant visual feedback
    this.notificationService.markNotificationAsRead(notificationId);

    // Mark all visible notifications as read before closing the panel
    this.markAllVisibleNotificationsAsRead();
    this.showNotifications = false;
    this.router.navigate(['/events', eventId]);
  }

  // Handle notification checkbox change (immediate deletion when unchecked)
  onNotificationCheckboxChange(event: Event, notification: Notification) {
    // Prevent event bubbling to avoid triggering navigation
    event.stopPropagation();

    const target = event.target as HTMLInputElement;

    // If checkbox is unchecked (false), delete the notification immediately
    if (!target.checked) {
      this.notificationService.deleteNotification(notification.id);
      // No toast message needed since the action is immediate and obvious
    }
  }

  // Handle clear all notifications with confirmation
  clearAllNotifications() {
    this.confirmationService.confirm({
      message: 'Are you sure you want to clear all notifications?',
      header: 'Clear All Notifications',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Yes, Clear All',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'p-button-danger',
      rejectButtonStyleClass: 'p-button-text',
      defaultFocus: 'reject',
      key: 'clearAllNotifications',
      accept: () => {
        this.notificationService.clearAllNotifications();
        this.messageService.add({
          severity: 'success',
          summary: 'All Notifications Cleared',
          detail: 'All notifications have been successfully cleared.',
          life: 3000,
        });
      },
    });
  }

  // Check if there are notifications to show the clear all button
  hasNotifications(): boolean {
    return this.notifications.length > 0;
  }

  logout() {
    this.authservice.logout();
    this.notificationService.reset();
    this.messageService.add({
      severity: 'success',
      summary: 'Logged Out',
      detail: 'You have been successfully logged out',
      life: 3000,
    });
    this.router.navigate(['/authentication/login']);
  }

  ngOnDestroy(): void {
    // Clean up avatar subscription when component is destroyed
    if (this.avatarSubscription) {
      this.avatarSubscription.unsubscribe();
      this.avatarSubscription = null;
    }

    // Clean up all notification subscriptions
    this.notificationSubscriptions.forEach((subscription) => {
      if (subscription) {
        subscription.unsubscribe();
      }
    });
    this.notificationSubscriptions = [];
  }
}
