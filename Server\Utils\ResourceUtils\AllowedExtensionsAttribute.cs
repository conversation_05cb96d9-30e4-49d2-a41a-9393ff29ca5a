﻿using System.ComponentModel.DataAnnotations;

namespace Server.Utils.ResourceUtils {
    public class AllowedExtensionsAttribute : ValidationAttribute {
        private readonly string[] _extensions;

        public AllowedExtensionsAttribute(string[] extensions) {
            _extensions = extensions;
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext) {
            if (value is IFormFile file) {
                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();

                if (string.IsNullOrEmpty(extension) || !_extensions.Contains(extension)) {
                    return new ValidationResult($"File extension not allowed. Allowed extensions: {string.Join(", ", _extensions)}");
                }
            }

            return ValidationResult.Success;
        }
    }
}
