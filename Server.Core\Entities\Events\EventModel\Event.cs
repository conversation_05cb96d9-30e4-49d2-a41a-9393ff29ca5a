﻿﻿using Server.Core.Entities.Events.EventContactDetailsModel;
using Server.Core.Entities.Events.EventLocationModel;
using Server.Core.Entities.UserManagement.UserModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Server.Core.Entities.Events.EventModel {
    public class Event {

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        // Basic Info
        [Required]
        public string Title { get; set; }

        [Required]
        public EventType Type { get; set; }

        [Required]
        public Category Category { get; set; }

        public int? Capacity { get; set; }

        [Required]
        public string Description { get; set; }

        public string EventImagePath { get; set; }

        [Required]
        public bool RequiresRegistration { get; set; }

        // Date and Time
        [Required]
        public DateTime EventStarts { get; set; }

        [Required]
        public DateTime EventEnds { get; set; }

        public TimeSpan StartTime { get; set; }

        public TimeSpan EndTime { get; set; }

        public bool DisplayStartTime { get; set; }

        public bool DisplayEndTime { get; set; }

        // Location Type (Venue or Online)
        [Required]
        public EventLocationType LocationType { get; set; }

        // Status and Approval
        public EventStatus Status { get; set; } = EventStatus.Draft;
        public bool IsApproved { get; set; }
        public DateTime? SubmittedOn { get; set; }
        public DateTime? ReviewedOn { get; set; }
        public string? ReviewedById { get; set; }
        public virtual ApplicationUser? ReviewedBy { get; set; }
        public string? RejectionReason { get; set; }

        // Submitter Information
        public string? SubmitterName { get; set; }

        // Organizer Information
        public string? OrganizerId { get; set; }
        public virtual ApplicationUser? Organizer { get; set; }

        // Timestamps
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual EventLocation? Location { get; set; }
        public virtual EventContactDetails? ContactDetails { get; set; }
    }

    public enum Category
    {
        CareersAndEmployment = 1,
        CommunityResources = 2,
        EarlyChildhood = 3,
        HealthWellness = 4,
        MaternalHealthCare = 5,
        RentalHousing = 6

    }

    public enum EventType
    {
        AppearanceOrSigning = 1,
        Attraction = 2,
        CampTripOrRetreat = 3,
        ClassTrainingOrWorkshop = 4,
        ConcertOrPerformance = 5,
        Conference = 6,
        Convention = 7,
        DinnerOrGala = 8,
        FestivalOrFair = 9,
        GamesOrCompetition = 10,
        MeetingOrNetworkingEvent = 11,
        Other = 12,
        PartyOrSocialGathering = 13,
        Rally = 14,
        Screening = 15,
        SeminarOrTalk = 16,
        Tour = 17,
        Tournament = 18,
        TradeShowConsumerShowOrExpo = 19



    }

    public enum EventLocationType {
        Venue = 1,
        Online = 2
    }

    public enum EventStatus {
        Draft = 1,
        Submitted = 2,
        Approved = 3,
        Rejected = 4,
        Cancelled = 5,
        Completed = 6
    }
}
