﻿using Microsoft.AspNetCore.Http;
using Server.Core.Entities.Resources.ContactDetailsModel;
using Server.Core.Entities.Resources.SocialMediaModel;
using Server.Utils.ResourceUtils;
using System.ComponentModel.DataAnnotations;

namespace Server.Core.Entities.Resources.ResourceModel {
    public class ResourceCreateDTO {

        [Required]
        public string OrganizationTitle { get; set; }
        public string SubTitle { get; set; }
        [Required]
        public string ResourceCategory { get; set; }

        // File uploads
        [AllowedExtensions(new[] { ".jpg", ".png", ".pdf" })]
        [MaxFileSize(5 * 1024 * 1024)] // 5MB
        public IFormFile ResourceImage { get; set; }

        [AllowedExtensions(new[] { ".jpg", ".png", ".pdf" })]
        [MaxFileSize(2 * 1024 * 1024)] // 2MB
        public IFormFile ResourceLogo { get; set; }

        // These will be set by the controller after saving the files
        public string ResourceImagePath { get; set; }
        public string ResourceLogoPath { get; set; }

        // Address
        [Required]
        public string Address { get; set; }
        [Required]
        public string City { get; set; }
        [Required]
        public string State { get; set; }
        [Required]

        public string ZipCode { get; set; }

        // Description
        [Required]
        public string ShortDescription { get; set; }
        [Required]
        public string LongDescription { get; set; }

        // Resource Type
        [Required]
        public ResourceType Type { get; set; }

        // Services
        [Required]
        [MinLength(1, ErrorMessage = "At least one service is required")]
        public List<string> Services { get; set; }

        // Contact Details
        public ContactDetailsDto ContactDetails { get; set; } = new ContactDetailsDto();

        // Social Media
        public SocialMediaDto SocialMedia { get; set; } = new SocialMediaDto();
    }
}
