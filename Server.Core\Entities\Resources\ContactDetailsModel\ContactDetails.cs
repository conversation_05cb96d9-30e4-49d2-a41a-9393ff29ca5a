﻿using Server.Core.Entities.Resources.ResourceModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Server.Core.Entities.Resources.ContactDetailsModel {
    public class ContactDetails {

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public int ResourceId { get; set; }
        public string ContactName { get; set; }

        public string ContactNo { get; set; }

        public string Website { get; set; }
        public string Email { get; set; }

        // Navigation property
        public virtual Resource Resource { get; set; }
    }
}
