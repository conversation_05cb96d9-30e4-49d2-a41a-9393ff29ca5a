# Git
.git
.gitignore
.gitattributes

# Docker
.docker
docker-compose.yml
docker-compose.*.yml
Dockerfile
.dockerignore

# Visual Studio
.vs/
.vscode/
*.user
*.userosscache
*.sln.docstates

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/
msbuild.log
msbuild.err
msbuild.wrn

# Node.js
node_modules/
npm-debug.log
yarn-error.log
.angular/cache

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Misc
*.swp
*.bak
*.tmp
*~
.DS_Store
Thumbs.db
