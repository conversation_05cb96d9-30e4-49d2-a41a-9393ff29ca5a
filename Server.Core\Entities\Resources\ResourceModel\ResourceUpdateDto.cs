﻿using Microsoft.AspNetCore.Http;
using Server.Core.Entities.Resources.ContactDetailsModel;
using Server.Core.Entities.Resources.SocialMediaModel;

namespace Server.Core.Entities.Resources.ResourceModel {
    public class ResourceUpdateDto {
        public int Id { get; set; }
        public string OrganizationTitle { get; set; }
        public string SubTitle { get; set; }
        public string ResourceCategory { get; set; }

        public IFormFile ResourceImage { get; set; }
        public IFormFile ResourceLogo { get; set; }
        public string ResourceImagePath { get; set; }
        public string ResourceLogoPath { get; set; }

        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string ZipCode { get; set; }

        public string ShortDescription { get; set; }
        public string LongDescription { get; set; }
        public ResourceType Type { get; set; }

        public List<string> Services { get; set; }
        public ContactDetailsDto ContactDetails { get; set; }
        public SocialMediaDto SocialMedia { get; set; }
    }
}
