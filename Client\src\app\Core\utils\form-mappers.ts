import { UserFormData, UserRequest } from '../Models/user-form.interface';

export function mapUserFormToPayload(
  formData: UserFormData,
  password: string,
  creatorId: string | undefined,
): UserRequest {
  return {
    Email: formData.email,
    FullName: formData.FullName,
    Password: password,
    PhoneNumber: formData.phoneNumber,
    Website: formData.website || '',
    Facebook: formData.facebook || '',
    Twitter: formData.twitter || '',
    Description: formData.description || '',
    Roles: [formData.role],
    IsActive: true,
    CreatedByName: '',
  };
}
