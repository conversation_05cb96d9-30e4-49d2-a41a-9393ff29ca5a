﻿using Microsoft.AspNetCore.Identity;

namespace Server.Core.Entities.UserManagement.UserModel {
    public class ApplicationUser : IdentityUser {
        public string FullName { get; set; } = string.Empty;

        public string Website { get; set; } = string.Empty;

        public string Description { get; set; } = string.Empty;

        public string createdByName { get; set; }

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

        public string Facebook { get; set; } = string.Empty;

        public string Twitter { get; set; } = string.Empty;

        public bool IsActive { get; set; }


    }
}
