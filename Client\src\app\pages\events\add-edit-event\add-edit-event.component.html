<!-- Toast for notifications -->
<p-toast position="top-right" styleClass="custom-toast"></p-toast>

<!-- Loading Overlay for form submission -->
<div *ngIf="isSubmitting" class="loading-overlay">
  <div class="loading-content">
    <p-progressSpinner
      strokeWidth="4"
      [style]="{ width: '50px', height: '50px' }"
    ></p-progressSpinner>
    <div class="mt-3 text-white">Processing your request...</div>
  </div>
</div>

<!-- Loading Spinner for initial load -->
<div
  *ngIf="isLoading"
  class="d-flex justify-content-center align-items-center"
  style="min-height: 400px"
>
  <p-progressSpinner
    strokeWidth="4"
    [style]="{ width: '50px', height: '50px' }"
  ></p-progressSpinner>
</div>

<!-- Error Message -->
<div *ngIf="!isLoading && error" class="alert alert-danger">
  {{ error }}
</div>

<!-- Form Content -->
<div *ngIf="!isLoading" class="event-form-container">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <div class="d-flex align-items-center">
      <a href="javascript:void(0)" class="back-button" routerLink="/events">
        <i class="pi pi-arrow-left"></i>
        <h2 class="mb-0">{{ isEditMode ? "Edit" : "Create New" }} Event</h2>
      </a>
    </div>
  </div>

  <!-- Detailed Validation Error Summary -->
  <!-- <div
    *ngIf="formSubmitted && eventForm.invalid"
    class="alert alert-danger mb-4"
  >
    <h6 class="mb-2">
      <i class="pi pi-exclamation-triangle me-2"></i>Please correct the
      following errors:
    </h6>
    <ul class="mb-0 ps-3">
      <li *ngIf="getBasicInfoControl('title')?.errors">
        Event title is required
      </li>
      <li *ngIf="getBasicInfoControl('type')?.errors">
        Event type is required
      </li>
      <li *ngIf="getBasicInfoControl('category')?.errors">
        Event category is required
      </li>
      <li *ngIf="getBasicInfoControl('description')?.errors">
        Event description is required
      </li>
      <li *ngIf="!isEditMode && !selectedFile">Event image is required</li>
      <li *ngIf="getDateTimeControl('eventStarts')?.errors">
        Event start date is required
      </li>
      <li *ngIf="getDateTimeControl('eventEnds')?.errors">
        Event end date is required
      </li>
      <li
        *ngIf="
          getLocationControl('locationType')?.value === locationTypes.Venue &&
          getLocationControl('address1')?.errors
        "
      >
        Address is required for venue events
      </li>
      <li
        *ngIf="
          getLocationControl('locationType')?.value === locationTypes.Venue &&
          getLocationControl('city')?.errors
        "
      >
        City is required for venue events
      </li>
      <li
        *ngIf="
          getLocationControl('locationType')?.value === locationTypes.Venue &&
          getLocationControl('state')?.errors
        "
      >
        State is required for venue events
      </li>
      <li
        *ngIf="
          getLocationControl('locationType')?.value === locationTypes.Venue &&
          getLocationControl('zipCode')?.errors
        "
      >
        ZIP Code is required for venue events
      </li>
      <li
        *ngIf="
          getLocationControl('locationType')?.value === locationTypes.Venue &&
          getLocationControl('country')?.errors
        "
      >
        Country is required for venue events
      </li>
      <li
        *ngIf="
          getLocationControl('locationType')?.value === locationTypes.Online &&
          getLocationControl('meetingId')?.errors
        "
      >
        Meeting ID is required for online events
      </li>
      <li
        *ngIf="
          getLocationControl('locationType')?.value === locationTypes.Online &&
          getLocationControl('passcode')?.errors
        "
      >
        Passcode is required for online events
      </li>
      <li
        *ngIf="getContactDetailsControl('contactName')?.errors?.['minlength']"
      >
        Contact name must be at least 2 characters
      </li>
      <li
        *ngIf="getContactDetailsControl('contactName')?.errors?.['maxlength']"
      >
        Contact name cannot exceed 100 characters
      </li>
      <li *ngIf="getContactDetailsControl('contactNo')?.errors?.['pattern']">
        Please enter a valid 10-digit phone number
      </li>
      <li
        *ngIf="
          getContactDetailsControl('email')?.errors?.['email'] ||
          getContactDetailsControl('email')?.errors?.['pattern']
        "
      >
        Please enter a valid email address
      </li>
      <li *ngIf="getContactDetailsControl('website')?.errors?.['pattern']">
        Please enter a valid website URL
      </li>
    </ul>
  </div> -->

  <form [formGroup]="eventForm" (ngSubmit)="onSubmit()" class="event-form">
    <!-- Basic Information Card -->
    <div class="card mb-4 border-0 shadow-sm">
      <div class="card-body">
        <h4 class="mb-4">Basic Information</h4>
        <div formGroupName="basicInfo">
          <div class="row g-3">
            <!-- Event Title -->
            <div class="col-md-12">
              <label for="title" class="form-label"
                >Event Title <span class="text-danger">*</span></label
              >
              <input
                pInputText
                type="text"
                id="title"
                placeholder="Enter event title"
                formControlName="title"
                class="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted || getBasicInfoControl('title')?.touched) &&
                    getBasicInfoControl('title')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted || getBasicInfoControl('title')?.touched) &&
                  getBasicInfoControl('title')?.invalid
                "
                class="text-danger small mt-1"
              >
                Event title is required
              </div>
            </div>

            <!-- Event Type -->
            <div class="col-md-4">
              <label for="type" class="form-label"
                >Event Type <span class="text-danger">*</span></label
              >
              <p-dropdown
                id="type"
                formControlName="type"
                placeholder="Select Type"
                [options]="eventTypeOptions"
                [showClear]="false"
                styleClass="w-100"
                [style]="{ height: '39px' }"
                optionLabel="name"
                optionValue="value"
                [appendTo]="'body'"
                [dropdownIcon]="'pi pi-chevron-down'"
                [baseZIndex]="1000"
                [autoZIndex]="true"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted || getBasicInfoControl('type')?.touched) &&
                    getBasicInfoControl('type')?.invalid,
                }"
              ></p-dropdown>
              <div
                *ngIf="
                  (formSubmitted || getBasicInfoControl('type')?.touched) &&
                  getBasicInfoControl('type')?.invalid
                "
                class="text-danger small mt-1"
              >
                Event type is required
              </div>
              <!-- Debug info -->
              <div
                class="small text-muted mt-1"
                *ngIf="eventTypeOptions.length === 0"
              >
                No event types available
              </div>
            </div>

            <!-- Event Category -->
            <div class="col-md-4">
              <label for="category" class="form-label"
                >Event Category <span class="text-danger">*</span></label
              >
              <p-dropdown
                id="category"
                formControlName="category"
                placeholder="Select Category"
                [options]="categoryOptions"
                [showClear]="false"
                styleClass="w-100"
                [style]="{ height: '39px' }"
                optionLabel="name"
                optionValue="value"
                [appendTo]="'body'"
                [dropdownIcon]="'pi pi-chevron-down'"
                [baseZIndex]="1000"
                [autoZIndex]="true"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted ||
                      getBasicInfoControl('category')?.touched) &&
                    getBasicInfoControl('category')?.invalid,
                }"
              ></p-dropdown>
              <div
                *ngIf="
                  (formSubmitted || getBasicInfoControl('category')?.touched) &&
                  getBasicInfoControl('category')?.invalid
                "
                class="text-danger small mt-1"
              >
                Event category is required
              </div>
            </div>

            <!-- Event Capacity -->
            <div class="col-md-4">
              <label for="capacity" class="form-label">Event Capacity</label>
              <input
                pInputText
                type="number"
                placeholder="Enter event capacity"
                id="capacity"
                formControlName="capacity"
                class="w-100"
                [style]="{ height: '39px' }"
                min="1"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted ||
                      getBasicInfoControl('capacity')?.touched) &&
                    getBasicInfoControl('capacity')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted || getBasicInfoControl('capacity')?.touched) &&
                  getBasicInfoControl('capacity')?.invalid
                "
                class="text-danger small mt-1"
              >
                Please enter a valid capacity
              </div>
            </div>

            <!-- Event Description -->
            <div class="col-md-12">
              <label for="description" class="form-label"
                >Event Description <span class="text-danger">*</span></label
              >
              <textarea
                pTextarea
                id="description"
                placeholder="Enter event description"
                formControlName="description"
                class="w-100"
                rows="5"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted ||
                      getBasicInfoControl('description')?.touched) &&
                    getBasicInfoControl('description')?.invalid,
                }"
              ></textarea>
              <div
                *ngIf="
                  (formSubmitted ||
                    getBasicInfoControl('description')?.touched) &&
                  getBasicInfoControl('description')?.invalid
                "
                class="text-danger small mt-1"
              >
                Event description is required
              </div>
            </div>

            <!-- Upload Image -->
            <div class="col-md-12">
              <label class="form-label"
                >Upload Image of Event<span class="text-danger">*</span></label
              >
              <div
                class="upload-container p-3 border rounded"
                [ngClass]="{
                  'border-danger':
                    formSubmitted && !isEditMode && !selectedFile,
                }"
              >
                <div class="row">
                  <div class="col-md-8">
                    <div class="mb-3">
                      <input
                        type="file"
                        class="form-control"
                        (change)="onFileSelected($event)"
                        accept=".jpg, .png"
                        [ngClass]="{
                          'is-invalid':
                            formSubmitted && !isEditMode && !selectedFile,
                        }"
                      />
                      <small class="text-muted d-block mt-1">
                        (Select jpg, png, Svg Only. Maximum file size: 5 MB)
                      </small>
                    </div>
                  </div>
                  <div
                    class="col-md-4"
                    *ngIf="imagePreview || (isEditMode && eventImageUrl)"
                  >
                    <div class="image-preview text-center">
                      <img
                        [src]="imagePreview || eventImageUrl"
                        alt="Event Image Preview"
                        class="img-fluid"
                        style="max-height: 150px"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                *ngIf="formSubmitted && !isEditMode && !selectedFile"
                class="text-danger small mt-1"
              >
                Event image is required
              </div>
            </div>

            <!-- Registration Required -->
            <div class="col-md-12">
              <label class="form-label"
                >Does this Event required registration?</label
              >
              <div class="d-flex align-items-center mt-2">
                <div class="me-4 d-flex align-items-center">
                  <p-radioButton
                    inputId="registrationYes"
                    formControlName="requiresRegistration"
                    [value]="true"
                  ></p-radioButton>
                  <label for="registrationYes" class="ms-2 mb-0">Yes</label>
                </div>
                <div class="d-flex align-items-center">
                  <p-radioButton
                    inputId="registrationNo"
                    formControlName="requiresRegistration"
                    [value]="false"
                  ></p-radioButton>
                  <label for="registrationNo" class="ms-2 mb-0">No</label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Date and Time Card -->
    <div class="card mb-4 border-0 shadow-sm">
      <div class="card-body">
        <h4 class="mb-4">Date and Time</h4>
        <div formGroupName="dateTime">
          <div class="row g-3">
            <!-- Event Start Date -->
            <div class="col-md-6">
              <label for="eventStarts" class="form-label"
                >Event Starts<span class="text-danger">*</span></label
              >
              <p-calendar
                id="eventStarts"
                formControlName="eventStarts"
                [showIcon]="true"
                styleClass="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted ||
                      getDateTimeControl('eventStarts')?.touched) &&
                    getDateTimeControl('eventStarts')?.errors,
                }"
                dateFormat="mm/dd/yy"
                [appendTo]="'body'"
                [baseZIndex]="1000"
                [minDate]="minDate"
                [readonlyInput]="true"
              ></p-calendar>
              <div
                *ngIf="
                  (formSubmitted ||
                    getDateTimeControl('eventStarts')?.touched) &&
                  getDateTimeControl('eventStarts')?.errors
                "
                class="text-danger small mt-1"
              >
                Event start date is required
              </div>
            </div>

            <!-- Start Time -->
            <div class="col-md-6">
              <label for="startTime" class="form-label">Start Time</label>
              <p-calendar
                id="startTime"
                formControlName="startTime"
                [timeOnly]="true"
                [showIcon]="true"
                styleClass="w-100"
                [style]="{ height: '39px' }"
                [appendTo]="'body'"
                [baseZIndex]="1000"
                [readonlyInput]="true"
                (onSelect)="validateStartTime($event)"
              ></p-calendar>
            </div>

            <!-- Event End Date -->
            <div class="col-md-6">
              <label for="eventEnds" class="form-label"
                >Event Ends<span class="text-danger">*</span></label
              >
              <p-calendar
                id="eventEnds"
                formControlName="eventEnds"
                [showIcon]="true"
                styleClass="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted ||
                      getDateTimeControl('eventEnds')?.touched) &&
                    getDateTimeControl('eventEnds')?.errors,
                }"
                dateFormat="mm/dd/yy"
                [appendTo]="'body'"
                [baseZIndex]="1000"
                [minDate]="getDateTimeControl('eventStarts')?.value || minDate"
                [readonlyInput]="true"
              ></p-calendar>
              <div
                *ngIf="
                  (formSubmitted || getDateTimeControl('eventEnds')?.touched) &&
                  getDateTimeControl('eventEnds')?.errors
                "
                class="text-danger small mt-1"
              >
                Event end date is required
              </div>
            </div>

            <!-- End Time -->
            <div class="col-md-6">
              <label for="endTime" class="form-label">End Time</label>
              <p-calendar
                id="endTime"
                formControlName="endTime"
                [timeOnly]="true"
                [showIcon]="true"
                styleClass="w-100"
                [style]="{ height: '39px' }"
                [appendTo]="'body'"
                [baseZIndex]="1000"
                [readonlyInput]="true"
                (onSelect)="validateEndTime($event)"
              ></p-calendar>
            </div>

            <!-- Display Time Options -->
            <div class="col-md-6">
              <div class="mt-4 d-flex align-items-start">
                <p-checkbox
                  [style]="{ marginTop: '0.25rem' }"
                  inputId="displayStartTime"
                  formControlName="displayStartTime"
                  [binary]="true"
                ></p-checkbox>
                <label for="displayStartTime" class="ms-2">
                  Display start time
                  <small class="text-muted d-block"
                    >Show the start time to attendees in the event list.
                  </small>
                </label>
              </div>
            </div>

            <div class="col-md-6">
              <div class="mt-4 d-flex align-items-start">
                <p-checkbox
                  [style]="{ marginTop: '0.25rem' }"
                  inputId="displayEndTime"
                  formControlName="displayEndTime"
                  [binary]="true"
                ></p-checkbox>
                <label for="displayEndTime" class="ms-2">
                  Display end time
                  <small class="text-muted d-block"
                    >Show the end time to attendees in the event list.
                  </small>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Location Card -->
    <div class="card mb-4 border-0 shadow-sm">
      <div class="card-body">
        <h4 class="mb-4">Location</h4>
        <div formGroupName="location">
          <!-- Location Type Selection -->
          <div class="location-tabs mb-4">
            <div class="tab-container">
              <div
                class="tab-item"
                [class.active]="
                  getLocationControl('locationType')?.value ===
                  locationTypes.Venue
                "
                (click)="
                  getLocationControl('locationType')?.setValue(
                    locationTypes.Venue
                  )
                "
              >
                Venue
              </div>
              <div
                class="tab-item"
                [class.active]="
                  getLocationControl('locationType')?.value ===
                  locationTypes.Online
                "
                (click)="
                  getLocationControl('locationType')?.setValue(
                    locationTypes.Online
                  )
                "
              >
                Online Event
              </div>
            </div>
          </div>

          <!-- Physical Location Fields -->
          <div
            *ngIf="
              getLocationControl('locationType')?.value === locationTypes.Venue
            "
            class="row g-3"
          >
            <div class="col-md-12">
              <label for="address1" class="form-label"
                >Address 1 <span class="text-danger">*</span></label
              >
              <input
                pInputText
                type="text"
                placeholder="Enter address 1"
                id="address1"
                formControlName="address1"
                class="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted ||
                      getLocationControl('address1')?.touched) &&
                    getLocationControl('address1')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted || getLocationControl('address1')?.touched) &&
                  getLocationControl('address1')?.invalid
                "
                class="text-danger small mt-1"
              >
                Address is required
              </div>
            </div>

            <div class="col-md-12">
              <label for="address2" class="form-label">Address 2</label>
              <input
                pInputText
                type="text"
                placeholder="Enter address 2"
                id="address2"
                formControlName="address2"
                class="w-100"
                [style]="{ height: '39px' }"
              />
            </div>

            <div class="col-md-6">
              <label for="city" class="form-label"
                >City <span class="text-danger">*</span></label
              >
              <input
                pInputText
                type="text"
                id="city"
                placeholder="Enter city"
                formControlName="city"
                class="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted || getLocationControl('city')?.touched) &&
                    getLocationControl('city')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted || getLocationControl('city')?.touched) &&
                  getLocationControl('city')?.invalid
                "
                class="text-danger small mt-1"
              >
                City is required
              </div>
            </div>

            <div class="col-md-6">
              <label for="state" class="form-label"
                >State <span class="text-danger">*</span></label
              >
              <input
                pInputText
                type="text"
                id="state"
                placeholder="Enter state"
                formControlName="state"
                class="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted || getLocationControl('state')?.touched) &&
                    getLocationControl('state')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted || getLocationControl('state')?.touched) &&
                  getLocationControl('state')?.invalid
                "
                class="text-danger small mt-1"
              >
                State is required
              </div>
            </div>

            <div class="col-md-6">
              <label for="zipCode" class="form-label"
                >ZIP Code <span class="text-danger">*</span></label
              >
              <input
                pInputText
                type="text"
                id="zipCode"
                placeholder="Enter zip code"
                formControlName="zipCode"
                class="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted || getLocationControl('zipCode')?.touched) &&
                    getLocationControl('zipCode')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted || getLocationControl('zipCode')?.touched) &&
                  getLocationControl('zipCode')?.invalid
                "
                class="text-danger small mt-1"
              >
                ZIP Code is required
              </div>
            </div>

            <div class="col-md-6">
              <label for="country" class="form-label"
                >Country <span class="text-danger">*</span></label
              >
              <input
                pInputText
                type="text"
                id="country"
                placeholder="Enter country"
                formControlName="country"
                class="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted || getLocationControl('country')?.touched) &&
                    getLocationControl('country')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted || getLocationControl('country')?.touched) &&
                  getLocationControl('country')?.invalid
                "
                class="text-danger small mt-1"
              >
                Country is required
              </div>
            </div>
          </div>

          <!-- Online Event Fields -->
          <div
            *ngIf="
              getLocationControl('locationType')?.value === locationTypes.Online
            "
            class="row g-3"
          >
            <div class="col-md-6">
              <label for="meetingId" class="form-label"
                >Meeting ID <span class="text-danger">*</span></label
              >
              <input
                pInputText
                type="text"
                id="meetingId"
                placeholder="Enter meeting ID"
                formControlName="meetingId"
                class="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted ||
                      getLocationControl('meetingId')?.touched) &&
                    getLocationControl('meetingId')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted || getLocationControl('meetingId')?.touched) &&
                  getLocationControl('meetingId')?.invalid
                "
                class="text-danger small mt-1"
              >
                Meeting ID is required
              </div>
            </div>

            <div class="col-md-6">
              <label for="passcode" class="form-label"
                >Passcode <span class="text-danger">*</span></label
              >
              <input
                pInputText
                type="text"
                id="passcode"
                placeholder="Enter passcode"
                formControlName="passcode"
                class="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted ||
                      getLocationControl('passcode')?.touched) &&
                    getLocationControl('passcode')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted || getLocationControl('passcode')?.touched) &&
                  getLocationControl('passcode')?.invalid
                "
                class="text-danger small mt-1"
              >
                Passcode is required
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Details Card -->
    <div class="card mb-4 border-0 shadow-sm">
      <div class="card-body">
        <h4 class="mb-4">Contact Details</h4>
        <div formGroupName="contactDetails">
          <div class="row g-3">
            <div class="col-md-6">
              <label for="contactName" class="form-label">Contact Name</label>
              <input
                pInputText
                type="text"
                id="contactName"
                placeholder="Enter contact name"
                formControlName="contactName"
                placeholder="John Doe"
                class="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted ||
                      getContactDetailsControl('contactName')?.touched) &&
                    getContactDetailsControl('contactName')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted ||
                    getContactDetailsControl('contactName')?.touched) &&
                  getContactDetailsControl('contactName')?.errors?.['required']
                "
                class="text-danger small mt-1"
              >
                Contact name is required
              </div>
              <div
                *ngIf="
                  (formSubmitted ||
                    getContactDetailsControl('contactName')?.touched) &&
                  getContactDetailsControl('contactName')?.errors?.['minlength']
                "
                class="text-danger small mt-1"
              >
                Contact name must be at least 2 characters
              </div>
              <div
                *ngIf="
                  (formSubmitted ||
                    getContactDetailsControl('contactName')?.touched) &&
                  getContactDetailsControl('contactName')?.errors?.['maxlength']
                "
                class="text-danger small mt-1"
              >
                Contact name cannot exceed 100 characters
              </div>
            </div>

            <div class="col-md-6">
              <label for="contactNo" class="form-label">Contact No</label>
              <input
                pInputText
                type="text"
                id="contactNo"
                formControlName="contactNo"
                placeholder="10-digit phone number"
                class="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted ||
                      getContactDetailsControl('contactNo')?.touched) &&
                    getContactDetailsControl('contactNo')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted ||
                    getContactDetailsControl('contactNo')?.touched) &&
                  getContactDetailsControl('contactNo')?.errors?.['required']
                "
                class="text-danger small mt-1"
              >
                Contact number is required
              </div>
              <div
                *ngIf="
                  (formSubmitted ||
                    getContactDetailsControl('contactNo')?.touched) &&
                  getContactDetailsControl('contactNo')?.errors?.['pattern']
                "
                class="text-danger small mt-1"
              >
                Please enter a valid 10-digit phone number
              </div>
            </div>

            <div class="col-md-6">
              <label for="website" class="form-label">Website</label>
              <input
                pInputText
                type="url"
                id="website"
                formControlName="website"
                placeholder="www.example.com"
                class="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted ||
                      getContactDetailsControl('website')?.touched) &&
                    getContactDetailsControl('website')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted ||
                    getContactDetailsControl('website')?.touched) &&
                  getContactDetailsControl('website')?.errors?.['required']
                "
                class="text-danger small mt-1"
              >
                Website is required
              </div>
              <div
                *ngIf="
                  (formSubmitted ||
                    getContactDetailsControl('website')?.touched) &&
                  getContactDetailsControl('website')?.errors?.['pattern']
                "
                class="text-danger small mt-1"
              >
                Please enter a valid website URL
              </div>
            </div>

            <div class="col-md-6">
              <label for="email" class="form-label">Email</label>
              <input
                pInputText
                type="email"
                id="email"
                formControlName="email"
                placeholder="<EMAIL>"
                class="w-100"
                [style]="{ height: '39px' }"
                [ngClass]="{
                  'ng-invalid ng-dirty':
                    (formSubmitted ||
                      getContactDetailsControl('email')?.touched) &&
                    getContactDetailsControl('email')?.invalid,
                }"
              />
              <div
                *ngIf="
                  (formSubmitted ||
                    getContactDetailsControl('email')?.touched) &&
                  getContactDetailsControl('email')?.errors?.['required']
                "
                class="text-danger small mt-1"
              >
                Email is required
              </div>
              <div
                *ngIf="
                  (formSubmitted ||
                    getContactDetailsControl('email')?.touched) &&
                  (getContactDetailsControl('email')?.errors?.['email'] ||
                    getContactDetailsControl('email')?.errors?.['pattern'])
                "
                class="text-danger small mt-1"
              >
                Please enter a valid email address
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Approval Options -->
    <div class="card mb-4 border-0 shadow-sm" *ngIf="isAdmin">
      <div class="card-body">
        <h4 class="mb-4">Approval Options</h4>
        <div class="d-flex align-items-start">
          <p-checkbox
            inputId="skipApproval"
            formControlName="skipApproval"
            [binary]="true"
          ></p-checkbox>
          <label for="skipApproval" class="ms-2">
            Skip Event Approval
            <small class="text-muted d-block"
              >Once checked, event will be activated and published straight away
              for user registration</small
            >
          </label>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="d-flex justify-content-end gap-2 mb-4">
      <button
        pButton
        type="button"
        class="p-button-outlined p-button-danger"
        routerLink="/events"
      >
        Cancel
      </button>
      <button
        pButton
        type="submit"
        class="p-button-danger"
        [disabled]="isSubmitting"
      >
        <span
          *ngIf="isSubmitting"
          class="spinner-border spinner-border-sm me-2"
          role="status"
          aria-hidden="true"
        ></span>
        {{ isEditMode ? "Update Event" : "Create Event" }}
      </button>
    </div>
  </form>
</div>
