export interface EventPaginationParams {
  pageNumber: number;
  pageSize: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: {
    searchTerm?: string;
    eventStartDate?: Date; // Legacy support
    eventStartDateFrom?: Date; // New parameter name to match form control
    eventStatus?: string;
    organizer?: string;
    approvalStatus?: string;
    type?: string | number;
    category?: string | number;
    submittedOn?: Date;
    eventReviewedOn?: Date;
  };
}

export interface PaginatedResponse<T> {
  items: T[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}
