import { Directive, Input, OnInit, TemplateRef, ViewContainerRef } from '@angular/core';
import { AuthService } from '../Services/auth.service';

@Directive({
  selector: '[appHasRole]',
  standalone: true,
})
export class HasRoleDirective implements OnInit {
  private requiredRoles: string[] = [];
  private isHidden = true;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private authService: AuthService,
  ) {}

  ngOnInit(): void {
    this.updateView();
  }

  @Input()
  set appHasRole(roles: string | string[]) {
    this.requiredRoles = Array.isArray(roles) ? roles : [roles];
    this.updateView();
  }

  private updateView(): void {
    const userInfo = this.authService.getUserInfo();
    
    if (!userInfo || !userInfo.role) {
      this.viewContainer.clear();
      this.isHidden = true;
      return;
    }

    const userRoles = Array.isArray(userInfo.role) ? userInfo.role : [userInfo.role];
    
    // Check if the user has any of the required roles
    const hasRequiredRole = this.requiredRoles.some(role => 
      userRoles.includes(role)
    );

    if (hasRequiredRole && this.isHidden) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.isHidden = false;
    } else if (!hasRequiredRole && !this.isHidden) {
      this.viewContainer.clear();
      this.isHidden = true;
    }
  }
}
