import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';
import { AuthService } from './auth.service';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { BehaviorSubject, Observable, Subscription, interval } from 'rxjs';
// Dialog functionality commented out as per request
// import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
// import { SessionExpiryDialogComponent } from '../../shared/components/session-expiry-dialog/session-expiry-dialog.component';

@Injectable({
  providedIn: 'root',
})
export class TokenExpiryService implements OnDestroy {
  private readonly CHECK_INTERVAL = 30000; // Check every 30 seconds
  private readonly WARNING_THRESHOLD = 5 * 60 * 1000; // Show warning 5 minutes before expiry
  // Critical threshold commented out as per request
  // private readonly CRITICAL_THRESHOLD = 1 * 60 * 1000; // Show critical warning 1 minute before expiry

  private timerSubscription: Subscription | null = null;
  // Dialog ref commented out as per request
  // private dialogRef: DynamicDialogRef | null = null;
  private timeRemaining = new BehaviorSubject<number>(0);
  private isWarningDisplayed = false;
  // Critical warning flag commented out as per request
  // private isCriticalWarningDisplayed = false;

  constructor(
    private authService: AuthService,
    private router: Router,
    private messageService: MessageService,
    // Dialog service commented out as per request
    // private dialogService: DialogService,
  ) {}

  startMonitoring(): void {
    // Clear any existing timer
    this.stopMonitoring();

    // Only start monitoring if user is logged in
    if (!this.authService.isLoggedIn()) {
      return;
    }

    // Start interval to check token expiry
    this.timerSubscription = interval(this.CHECK_INTERVAL).subscribe(() => {
      this.checkTokenExpiry();
    });

    // Do an immediate check
    this.checkTokenExpiry();
  }

  stopMonitoring(): void {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
      this.timerSubscription = null;
    }

    // Dialog closing commented out as per request
    // this.closeExpiryDialog();
    this.isWarningDisplayed = false;
    // Critical warning flag commented out as per request
    // this.isCriticalWarningDisplayed = false;
  }

  getTimeRemaining(): Observable<number> {
    return this.timeRemaining.asObservable();
  }

  private checkTokenExpiry(): void {
    // Skip if not logged in or on auth routes
    if (!this.authService.isLoggedIn() || this.isAuthRoute()) {
      return;
    }

    const token = this.authService.getToken();
    if (!token) {
      this.handleExpiredToken();
      return;
    }

    const decodedToken = this.authService.decodeToken(token);
    if (!decodedToken || !decodedToken.exp) {
      this.handleExpiredToken();
      return;
    }

    const expiryTime = decodedToken.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const timeRemaining = expiryTime - currentTime;

    // Update time remaining
    this.timeRemaining.next(timeRemaining);

    // Handle different expiry scenarios
    if (timeRemaining <= 0) {
      this.handleExpiredToken();
      // Critical warning dialog commented out as per request
      // } else if (
      //   timeRemaining <= this.CRITICAL_THRESHOLD &&
      //   !this.isCriticalWarningDisplayed
      // ) {
      //   this.showCriticalExpiryWarning(timeRemaining);
    } else if (
      timeRemaining <= this.WARNING_THRESHOLD &&
      !this.isWarningDisplayed
    ) {
      this.showExpiryWarning(timeRemaining);
    }
  }

  private handleExpiredToken(): void {
    // Dialog closing commented out as per request
    // this.closeExpiryDialog();
    this.authService.logout();

    // Only redirect if not already on an auth route
    if (!this.isAuthRoute()) {
      this.messageService.add({
        severity: 'error',
        summary: 'Session Expired',
        detail: 'Your session has expired. Please log in again.',
        life: 5000,
      });

      this.router.navigate(['/authentication/login'], {
        queryParams: { returnUrl: this.router.url },
      });
    }
  }

  private showExpiryWarning(timeRemaining: number): void {
    this.isWarningDisplayed = true;

    this.messageService.add({
      severity: 'warn',
      summary: 'Session Expiring Soon',
      detail:
        'Your session will expire in about 5 minutes. Please save your work.',
      life: 10000,
    });
  }

  // Critical warning dialog commented out as per request
  /*
  private showCriticalExpiryWarning(timeRemaining: number): void {
    this.isCriticalWarningDisplayed = true;
    this.closeExpiryDialog();

    this.dialogRef = this.dialogService.open(SessionExpiryDialogComponent, {
      header: 'Session Expiring Soon',
      width: '400px',
      closable: false,
      closeOnEscape: false,
      data: {
        timeRemaining: Math.floor(timeRemaining / 1000), // Convert to seconds
      },
    });

    this.dialogRef.onClose.subscribe((action: string) => {
      if (action === 'extend') {
        // For now, just reset the warning flags
        // In a real implementation, this would call a token refresh API
        this.isWarningDisplayed = false;
        this.isCriticalWarningDisplayed = false;
      } else if (action === 'logout') {
        this.authService.logout();
        this.router.navigate(['/authentication/login']);
      }
    });
  }

  private closeExpiryDialog(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
      this.dialogRef = null;
    }
  }
  */

  /**
   * Check if current route is an auth route
   */
  private isAuthRoute(): boolean {
    const currentUrl = this.router.url;
    return (
      currentUrl.includes('/authentication/login') ||
      currentUrl.includes('/authentication/verify-otp')
    );
  }

  ngOnDestroy(): void {
    this.stopMonitoring();
  }
}
