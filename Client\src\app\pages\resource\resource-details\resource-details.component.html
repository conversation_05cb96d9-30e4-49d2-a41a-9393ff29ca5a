<!-- Resource Details Page Container -->
<div class="resource-page-container">
  <!-- Navigation Header -->
  <div class="resource-nav-header">
    <div class="container d-flex justify-content-between align-items-center">
      <a href="javascript:void(0)" class="back-button" (click)="goBack()">
        <i class="bi bi-arrow-left"></i>
        <span>Resource Details</span>
      </a>
      <button class="btn edit-button" (click)="editResource()">
        <i class="bi bi-pencil-fill"></i>
        Edit
      </button>
    </div>
  </div>

  <!-- Toast for notifications -->
  <p-toast></p-toast>

  <!-- Loading Spinner -->
  <div
    *ngIf="isLoading"
    class="d-flex justify-content-center align-items-center"
    style="min-height: 400px"
  >
    <p-progressSpinner
      strokeWidth="4"
      [style]="{ width: '50px', height: '50px' }"
    ></p-progressSpinner>
  </div>

  <!-- Resource Details Content -->
  <div
    *ngIf="!isLoading && !error && resource"
    class="resource-details-container"
  >
    <!-- Main Image Banner -->
    <div class="cover-image">
      <img
        [src]="
          getFullImagePath(resource.resourceImageUrl) ||
          'assets/images/placeholder.jpg'
        "
        alt="Resource Banner"
        class="resource-banner-img"
      />
    </div>

    <!-- Resource Details Card -->
    <div class="content-container">
      <!-- Header Section with Organization Info, Location and Description -->
      <div class="content-section">
        <!-- Organization Header with Logo -->
        <div class="d-flex align-items-start mb-4">
          <div class="organization-logo me-3">
            <img
              [src]="
                getFullImagePath(resource.resourceLogoUrl) ||
                'assets/images/placeholder.jpg'
              "
              alt="Organization Logo"
            />
          </div>
          <div class="organization-info">
            <h3 class="mb-1">{{ resource.organizationTitle }}</h3>
            <p class="text-muted mb-1">{{ resource.subTitle }}</p>
            <div class="mb-3">
              <span class="category-badge">
                {{ resource.resourceCategory }}
              </span>
            </div>
          </div>
        </div>

        <!-- Location with Label -->
        <div class="mb-4">
          <h5 class="section-title">Location</h5>
          <div class="d-flex align-items-start">
            <i class="bi bi-geo-alt me-2 text-danger"></i>
            <span>
              {{ resource.address }}, {{ resource.city }}, {{ resource.state }}
              {{ resource.zipCode }}
            </span>
          </div>
        </div>

        <!-- Short Description -->
        <p>{{ resource.shortDescription }}</p>
      </div>

      <!-- Services Section -->
      <div
        class="content-section"
        *ngIf="resource.services && resource.services.length > 0"
      >
        <h5 class="section-title">Services</h5>
        <div class="row">
          <div class="col-md-6 mb-2" *ngFor="let service of resource.services">
            <div class="d-flex align-items-center">
              <div class="service-bullet me-2">
                <i class="bi bi-circle-fill"></i>
              </div>
              <span>{{ service }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Long Description Section -->
      <div class="content-section">
        <h5 class="section-title">Long Description</h5>
        <p>{{ resource.longDescription }}</p>
      </div>

      <!-- Contact Details Section -->
      <div class="content-section" *ngIf="resource.contactDetails">
        <h5 class="section-title">Contact Details</h5>
        <div class="row">
          <div class="col-md-6 mb-3">
            <div class="d-flex align-items-start">
              <i class="bi bi-person me-2"></i>
              <div>
                <p class="text-muted mb-1">Contact Name</p>
                <p class="mb-0 fw-medium">
                  {{
                    getNestedProperty(resource, "contactDetails.contactName") ||
                      getNestedProperty(
                        resource,
                        "contactDetails.ContactName"
                      ) ||
                      resource.organizationTitle
                  }}
                </p>
              </div>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <div class="d-flex align-items-start">
              <i class="bi bi-telephone me-2"></i>
              <div>
                <p class="text-muted mb-1">Contact No</p>
                <p class="mb-0 fw-medium">
                  {{
                    getNestedProperty(resource, "contactDetails.contactNo") ||
                      getNestedProperty(resource, "contactDetails.ContactNo")
                  }}
                </p>
              </div>
            </div>
          </div>
          <div
            class="col-md-6 mb-3"
            *ngIf="getNestedProperty(resource, 'contactDetails.email')"
          >
            <div class="d-flex align-items-start">
              <i class="bi bi-envelope me-2"></i>
              <div>
                <p class="text-muted mb-1">Email</p>
                <p class="mb-0 fw-medium">
                  <a
                    href="mailto:{{
                      getNestedProperty(resource, 'contactDetails.email')
                    }}"
                    class="text-decoration-none"
                  >
                    {{ getNestedProperty(resource, "contactDetails.email") }}
                  </a>
                </p>
              </div>
            </div>
          </div>
          <div
            class="col-md-6 mb-3"
            *ngIf="getNestedProperty(resource, 'contactDetails.website')"
          >
            <div class="d-flex align-items-start">
              <i class="bi bi-globe me-2"></i>
              <div>
                <p class="text-muted mb-1">Website</p>
                <p class="mb-0 fw-medium">
                  <a
                    [href]="
                      getNestedProperty(resource, 'contactDetails.website')
                    "
                    target="_blank"
                    class="text-decoration-none"
                  >
                    {{ getNestedProperty(resource, "contactDetails.website") }}
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Social Media Section -->
      <div class="content-section" *ngIf="resource.socialMedia">
        <h5 class="section-title">Social Media</h5>
        <div class="row">
          <div
            class="col-md-6 mb-3"
            *ngIf="getNestedProperty(resource, 'socialMedia.facebook')"
          >
            <div class="d-flex align-items-start">
              <i class="bi bi-facebook me-2"></i>
              <div>
                <p class="text-muted mb-1">Facebook</p>
                <p class="mb-0 fw-medium">
                  <a
                    [href]="getNestedProperty(resource, 'socialMedia.facebook')"
                    target="_blank"
                    class="text-decoration-none"
                  >
                    {{ getNestedProperty(resource, "socialMedia.facebook") }}
                  </a>
                </p>
              </div>
            </div>
          </div>
          <div
            class="col-md-6 mb-3"
            *ngIf="getNestedProperty(resource, 'socialMedia.instagram')"
          >
            <div class="d-flex align-items-start">
              <i class="bi bi-instagram me-2"></i>
              <div>
                <p class="text-muted mb-1">Instagram</p>
                <p class="mb-0 fw-medium">
                  <a
                    [href]="
                      getNestedProperty(resource, 'socialMedia.instagram')
                    "
                    target="_blank"
                    class="text-decoration-none"
                  >
                    {{ getNestedProperty(resource, "socialMedia.instagram") }}
                  </a>
                </p>
              </div>
            </div>
          </div>
          <div
            class="col-md-6 mb-3"
            *ngIf="getNestedProperty(resource, 'socialMedia.twitter')"
          >
            <div class="d-flex align-items-start">
              <i class="pi pi-twitter me-2"></i>
              <div>
                <p class="text-muted mb-1">Twitter (X)</p>
                <p class="mb-0 fw-medium">
                  <a
                    [href]="getNestedProperty(resource, 'socialMedia.twitter')"
                    target="_blank"
                    class="text-decoration-none"
                  >
                    {{ getNestedProperty(resource, "socialMedia.twitter") }}
                  </a>
                </p>
              </div>
            </div>
          </div>
          <div
            class="col-md-6 mb-3"
            *ngIf="
              getNestedProperty(resource, 'socialMedia.linkedin') ||
              getNestedProperty(resource, 'socialMedia.linkedIn')
            "
          >
            <div class="d-flex align-items-start">
              <i class="bi bi-linkedin me-2"></i>
              <div>
                <p class="text-muted mb-1">LinkedIn</p>
                <p class="mb-0 fw-medium">
                  <a
                    [href]="
                      getNestedProperty(resource, 'socialMedia.linkedin') ||
                      getNestedProperty(resource, 'socialMedia.linkedIn')
                    "
                    target="_blank"
                    class="text-decoration-none"
                  >
                    {{
                      getNestedProperty(resource, "socialMedia.linkedin") ||
                        getNestedProperty(resource, "socialMedia.linkedIn")
                    }}
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Partner Resource Section (if applicable) -->
      <!-- <div
        class="content-section"
        *ngIf="resource.resourceTypeName === 'SWPNPartner'"
      >
        <h5 class="mb-3">Partner Resource</h5>
        <div class="d-flex align-items-start mb-3">
          <div class="partner-logo me-3">
            <img
              src="assets/images/imagine-logo.png"
              alt="Partner Logo"
              width="60"
              height="60"
            />
          </div>
          <div>
            <h6 class="mb-1">Imagine, A Center For Coping With Loss</h6>
            <p class="text-muted mb-1">South Ward Supports Our Families</p>
            <p class="mb-0 badge bg-light text-dark">Community Resources</p>
          </div>
        </div>
        <p>{{ resource.shortDescription }}</p>
      </div> -->
    </div>
  </div>
</div>
