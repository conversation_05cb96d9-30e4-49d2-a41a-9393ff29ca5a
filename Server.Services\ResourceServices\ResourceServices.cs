﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Server.Core.Entities.Resources.ContactDetailsModel;
using Server.Core.Entities.Resources.ResourceModel;
using Server.Core.Entities.Resources.ServiceModel;
using Server.Core.Entities.Resources.SocialMediaModel;
using Server.Core.Entities.UserManagement.ResponseModel;
using Server.Core.Pagination.PagedResponseModel;
using Server.Core.Pagination.PaginationParameterForResourceModel;
using Server.EntityFramworkCore.Data;
using Server.Services.S3Services;


namespace Server.Services.ResourceServices {
    public class ResourceServices : IResourceService {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;
        private readonly IS3Service _s3Service;

        public ResourceServices(
            ApplicationDbContext context,
            IMapper mapper,
            IHostEnvironment environment,
            IS3Service s3Service) {
            _context = context;
            _mapper = mapper;
            _s3Service = s3Service;
        }

        public async Task<PagedResponse<ResourceResponseDto>> GetAllResource(PaginationParameterEvents parameters) {
            try {
                // Start with the base query
                var query = _context.Resources.AsQueryable();

                // Apply filters
                if (!string.IsNullOrWhiteSpace(parameters.OrganizationalTitle))
                    query = query.Where(r => r.OrganizationTitle.Contains(parameters.OrganizationalTitle));

                if (!string.IsNullOrWhiteSpace(parameters.Category))
                    query = query.Where(r => r.ResourceCategory.Contains(parameters.Category));

                // Fix for ResourceType filtering - convert string to enum and compare directly
                if (!string.IsNullOrWhiteSpace(parameters.ResourceType) && Enum.TryParse<ResourceType>(parameters.ResourceType, out var resourceTypeEnum))
                    query = query.Where(r => r.Type == resourceTypeEnum);

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(parameters.SortField)) {
                    var isAscending = parameters.SortOrder?.ToLower() != "desc";
                    query = ApplySorting(query, parameters.SortField, isAscending);
                }
                else {
                    // Default sorting by CreatedAt descending
                    query = query.OrderByDescending(r => r.CreatedAt);
                }

                // Get total count before pagination
                var totalItems = await query.CountAsync();

                // Apply pagination
                var items = await query
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToListAsync();

                // Map to DTOs
                var resourceDtos = _mapper.Map<List<ResourceResponseDto>>(items);

                // Set the correct URLs for S3 or local storage
                foreach (var dto in resourceDtos) {
                    var resource = items.FirstOrDefault(r => r.Id == dto.Id);
                    if (resource != null) {
                        if (!string.IsNullOrEmpty(resource.ResourceImagePath)) {
                            dto.ResourceImageUrl = _s3Service.GetFileUrl(resource.ResourceImagePath);
                        }

                        if (!string.IsNullOrEmpty(resource.ResourceLogoPath)) {
                            dto.ResourceLogoUrl = _s3Service.GetFileUrl(resource.ResourceLogoPath);
                        }
                    }
                }

                return new PagedResponse<ResourceResponseDto>(resourceDtos, totalItems, parameters.PageNumber, parameters.PageSize);
            }
            catch (Exception ex) {
                // Log the exception details here if you have logging configured
                throw;
            }
        }

        public async Task<ResourceResponseDto?> GetResourceById(int id) {
            var resource = await _context.Resources
                .Include(r => r.Services)
                .Include(r => r.ContactDetails)
                .Include(r => r.SocialMedia)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (resource == null) {
                return null;
            }

            var resourceDto = _mapper.Map<ResourceResponseDto>(resource);

            // Set the correct URLs for S3 or local storage
            if (!string.IsNullOrEmpty(resource.ResourceImagePath)) {
                resourceDto.ResourceImageUrl = _s3Service.GetFileUrl(resource.ResourceImagePath);
            }

            if (!string.IsNullOrEmpty(resource.ResourceLogoPath)) {
                resourceDto.ResourceLogoUrl = _s3Service.GetFileUrl(resource.ResourceLogoPath);
            }

            return resourceDto;
        }

        public async Task<Response> CreateResource(ResourceCreateDTO resourceDto) {
            try {
                // Quick validation checks
                if (resourceDto == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Resource data is null"
                    };
                }

                // Validate required fields - do this before any database operations
                if (string.IsNullOrEmpty(resourceDto.OrganizationTitle)) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Organization title is required"
                    };
                }

                if (string.IsNullOrEmpty(resourceDto.ResourceCategory)) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Resource category is required"
                    };
                }

                if (resourceDto.Services == null || resourceDto.Services.Count == 0) {
                    return new Response {
                        IsSuccess = false,
                        Message = "At least one service is required"
                    };
                }

                // Check for duplicate title - use a more efficient query with cancellation token
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5)); // 5-second timeout
                bool titleExists = await _context.Resources
                    .AsNoTracking() // Don't track entities for better performance
                    .AnyAsync(r => r.OrganizationTitle == resourceDto.OrganizationTitle, cts.Token);

                if (titleExists) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Resource with this title already exists"
                    };
                }

                // Process file uploads in parallel
                Dictionary<string, string> uploadedFiles = null;
                if (resourceDto.ResourceImage != null || resourceDto.ResourceLogo != null) {
                    var filesToUpload = new Dictionary<string, IFormFile>();

                    if (resourceDto.ResourceImage != null) {
                        filesToUpload.Add("ResourceImage", resourceDto.ResourceImage);
                    }

                    if (resourceDto.ResourceLogo != null) {
                        filesToUpload.Add("ResourceLogo", resourceDto.ResourceLogo);
                    }

                    // Upload all files in parallel
                    if (filesToUpload.Count > 0) {
                        uploadedFiles = await ((OptimizedS3Service)_s3Service).UploadMultipleFilesAsync(filesToUpload);
                    }
                }

                // Create the resource entity
                var resource = _mapper.Map<Resource>(resourceDto);
                resource.CreatedAt = DateTime.UtcNow;

                // Set file paths from parallel uploads
                if (uploadedFiles != null) {
                    if (uploadedFiles.TryGetValue("ResourceImage", out string imagePath)) {
                        resource.ResourceImagePath = imagePath;
                    }

                    if (uploadedFiles.TryGetValue("ResourceLogo", out string logoPath)) {
                        resource.ResourceLogoPath = logoPath;
                    }
                }

                // Ensure services are properly mapped - use more efficient LINQ
                if (resourceDto.Services?.Count > 0) {
                    resource.Services = resourceDto.Services
                        .Where(s => !string.IsNullOrWhiteSpace(s))
                        .Select(serviceName => new Service { ServiceName = serviceName })
                        .ToList();
                }

                // Ensure contact details are properly mapped
                if (resourceDto.ContactDetails != null) {
                    resource.ContactDetails = new ContactDetails {
                        ContactName = resourceDto.ContactDetails.ContactName,
                        ContactNo = resourceDto.ContactDetails.ContactNo,
                        Email = resourceDto.ContactDetails.Email,
                        Website = resourceDto.ContactDetails.Website
                    };
                }

                // Ensure social media details are properly mapped
                if (resourceDto.SocialMedia != null) {
                    resource.SocialMedia = new SocialMedia {
                        Facebook = resourceDto.SocialMedia.Facebook,
                        Twitter = resourceDto.SocialMedia.Twitter,
                        Instagram = resourceDto.SocialMedia.Instagram,
                        LinkedIn = resourceDto.SocialMedia.LinkedIn,
                    };
                }

                // Use a single database operation with a timeout
                await _context.Resources.AddAsync(resource);
                await _context.SaveChangesAsync(cts.Token);

                return new Response {
                    IsSuccess = true,
                    Message = "Resource created successfully"
                };
            }
            catch (OperationCanceledException) {
                return new Response {
                    IsSuccess = false,
                    Message = "The operation timed out. Please try again."
                };
            }
            catch (Exception ex) {
                return new Response {
                    IsSuccess = false,
                    Message = $"Failed to create resource: {ex.Message}",
                    Data = ex.StackTrace
                };
            }
        }

        public async Task<Response> UpdateResource(ResourceUpdateDto resourceDto) {
            try {
                // Find the resource with a single database query
                var existingResource = await _context.Resources.FindAsync(resourceDto.Id);
                if (existingResource == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Resource not found"
                    };
                }

                // Process file uploads first to handle any potential errors before database operations
                string resourceImagePath = existingResource.ResourceImagePath;
                string resourceLogoPath = existingResource.ResourceLogoPath;

                if (resourceDto.ResourceImage != null) {
                    // Delete old image if exists - do this after successful upload to prevent data loss
                    string newImagePath = await UploadFile(resourceDto.ResourceImage, "ResourceImages");

                    if (!string.IsNullOrEmpty(newImagePath)) {
                        if (!string.IsNullOrEmpty(existingResource.ResourceImagePath)) {
                            DeleteFile(existingResource.ResourceImagePath);
                        }
                        resourceImagePath = newImagePath;
                    }
                }

                if (resourceDto.ResourceLogo != null) {
                    // Delete old logo if exists - do this after successful upload to prevent data loss
                    string newLogoPath = await UploadFile(resourceDto.ResourceLogo, "ResourceLogos");

                    if (!string.IsNullOrEmpty(newLogoPath)) {
                        if (!string.IsNullOrEmpty(existingResource.ResourceLogoPath)) {
                            DeleteFile(existingResource.ResourceLogoPath);
                        }
                        resourceLogoPath = newLogoPath;
                    }
                }

                // Update properties
                _mapper.Map(resourceDto, existingResource);
                existingResource.UpdatedAt = DateTime.UtcNow;
                existingResource.ResourceImagePath = resourceImagePath;
                existingResource.ResourceLogoPath = resourceLogoPath;

                // Use a single database operation
                _context.Resources.Update(existingResource);
                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Resource updated successfully"
                };
            }
            catch (Exception ex) {
                return new Response {
                    IsSuccess = false,
                    Message = "Failed to update resource",
                    Data = ex.Message
                };
            }
        }

        public async Task<Response> DeleteResource(int id) {
            try {
                var resource = await _context.Resources.FindAsync(id);
                if (resource == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Resource not found"
                    };
                }

                // Delete associated files
                if (!string.IsNullOrEmpty(resource.ResourceImagePath)) {
                    DeleteFile(resource.ResourceImagePath);
                }
                if (!string.IsNullOrEmpty(resource.ResourceLogoPath)) {
                    DeleteFile(resource.ResourceLogoPath);
                }

                _context.Resources.Remove(resource);
                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Resource deleted successfully"
                };
            }
            catch (Exception ex) {
                return new Response {
                    IsSuccess = false,
                    Message = "Failed to delete resource",
                    Data = ex.Message
                };
            }
        }

        private IQueryable<Resource> ApplySorting(IQueryable<Resource> query, string sortField, bool isAscending) {
            return sortField.ToLower() switch {
                "organizationtitle" => isAscending ? query.OrderBy(r => r.OrganizationTitle) : query.OrderByDescending(r => r.OrganizationTitle),
                "resourcecategory" => isAscending ? query.OrderBy(r => r.ResourceCategory) : query.OrderByDescending(r => r.ResourceCategory),
                "type" => isAscending ? query.OrderBy(r => r.Type) : query.OrderByDescending(r => r.Type),
                "createdat" => isAscending ? query.OrderBy(r => r.CreatedAt) : query.OrderByDescending(r => r.CreatedAt),
                "updatedat" => isAscending ? query.OrderBy(r => r.UpdatedAt) : query.OrderByDescending(r => r.UpdatedAt),
                _ => query.OrderByDescending(r => r.CreatedAt)
            };
        }

        public async Task<string> UploadFile(IFormFile file, string folderName) {
            if (file == null || file.Length == 0)
                return null;

            try {
                // Use S3 service to upload the file
                string key = await _s3Service.UploadFileAsync(file, folderName);

                // Return the key (path) of the file in S3
                return key;
            }
            catch (Exception ex) {
                // Log the exception
                Console.WriteLine($"Error uploading file to S3: {ex.Message}");
                throw; // Rethrow to handle at the service level
            }
        }

        public void DeleteFile(string filePath) {
            if (string.IsNullOrEmpty(filePath))
                return;

            try {
                // Use S3 service to delete the file
                _s3Service.DeleteFileAsync(filePath).Wait();
            }
            catch (Exception ex) {
                // Log the exception
                Console.WriteLine($"Error deleting file from S3: {ex.Message}");
            }
        }
    }
}



