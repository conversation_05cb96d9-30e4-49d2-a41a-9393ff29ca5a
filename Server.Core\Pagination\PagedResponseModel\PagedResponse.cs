﻿namespace Server.Core.Pagination.PagedResponseModel {
    public class PagedResponse<T> {

        public IEnumerable<T> Items { get; set; }
        public int TotalItems { get; set; }
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }

        public PagedResponse(IEnumerable<T> items, int totalItems, int pageNumber, int pageSize) {
            Items = items;
            TotalItems = totalItems;
            CurrentPage = pageNumber;
            PageSize = pageSize;
            TotalPages = (int)Math.Ceiling(totalItems / (double)pageSize);
        }
    }
}
