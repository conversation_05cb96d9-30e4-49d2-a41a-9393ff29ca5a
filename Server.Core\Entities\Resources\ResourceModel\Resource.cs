﻿using Server.Core.Entities.Resources.ContactDetailsModel;
using Server.Core.Entities.Resources.ServiceModel;
using Server.Core.Entities.Resources.SocialMediaModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Server.Core.Entities.Resources.ResourceModel {
    public class Resource {

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        // Basic Info
        public string OrganizationTitle { get; set; }
        public string SubTitle { get; set; }
        public string ResourceCategory { get; set; }
        public string ResourceImagePath { get; set; }
        public string ResourceLogoPath { get; set; }

        // Address
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string ZipCode { get; set; }

        // Description
        public string ShortDescription { get; set; }
        public string LongDescription { get; set; }

        // Resource Type
        public ResourceType Type { get; set; }

        // Timestamps
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<Service> Services { get; set; }
        public virtual ContactDetails ContactDetails { get; set; }
        public virtual SocialMedia SocialMedia { get; set; }

    }

    public enum ResourceType {
        ExternalPartner = 1,
        SouthWardPromiseNeighbourhood = 2,
        SWPNPartner = 3
    }
}
