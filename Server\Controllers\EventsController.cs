﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Server.Core.Entities.Events.EventModel;
using Server.Core.Entities.UserManagement.UserModel;
using Server.Core.Pagination.PaginationParameterForEventModel;
using Server.Services.EventServices;
using System.Security.Claims;

namespace Server.API.Controllers {
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class EventsController : ControllerBase {
        private readonly IEventService _eventService;
        private readonly UserManager<ApplicationUser> _userManager;

        public EventsController(IEventService eventService, UserManager<ApplicationUser> userManager) {
            _eventService = eventService;
            _userManager = userManager;
        }

        // GET: api/Events/GetAllEvents
        [HttpGet("GetAllEvents")]
        public async Task<IActionResult> GetAllEvents([FromQuery] PaginationParameterEvent parameters) {
            try {
                var result = await _eventService.GetAllEvents(parameters);
                var apiResponse = new Server.Core.Entities.UserManagement.ResponseModel.ApiResponse<Server.Core.Pagination.PagedResponseModel.PagedResponse<Server.Core.Entities.Events.EventModel.EventResponseDto>> {
                    IsSuccess = true,
                    Message = "Events retrieved successfully",
                    Data = result
                };
                return Ok(apiResponse);
            }
            catch (Exception ex) {
                var apiResponse = new Server.Core.Entities.UserManagement.ResponseModel.ApiResponse<Server.Core.Pagination.PagedResponseModel.PagedResponse<Server.Core.Entities.Events.EventModel.EventResponseDto>> {
                    IsSuccess = false,
                    Message = $"Error retrieving events: {ex.Message}",
                    Data = null
                };
                return BadRequest(apiResponse);
            }
        }

        // GET: api/Events/GetEventById/5
        [HttpGet("GetEventById/{id}")]
        public async Task<IActionResult> GetEventById(int id) {
            try {
                var result = await _eventService.GetEventById(id);
                if (result.IsSuccess) {
                    return Ok(result);
                }
                return NotFound(result);
            }
            catch (Exception ex) {
                var apiResponse = new Server.Core.Entities.UserManagement.ResponseModel.ApiResponse<Server.Core.Entities.Events.EventModel.EventResponseDto> {
                    IsSuccess = false,
                    Message = $"Error retrieving event: {ex.Message}",
                    Data = null
                };
                return BadRequest(apiResponse);
            }
        }

        // POST: api/Events/CreateEvent
        [HttpPost("CreateEvent")]
        public async Task<IActionResult> CreateEvent([FromForm] EventCreateDto dto) {
            if (!ModelState.IsValid) {
                return BadRequest(ModelState);
            }

            // Get the current user's full name from the claims
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser != null) {
                dto.SubmitterName = currentUser.FullName;
            }

            // Handle file upload
            if (dto.EventImage != null) {
                var uploadResult = await _eventService.UploadFile(dto.EventImage, "EventImages");
                Console.WriteLine($"EventsController.CreateEvent: File uploaded, path: {uploadResult}");
                dto.EventImagePath = uploadResult;
            }

            var result = await _eventService.CreateEvent(dto);
            if (result.IsSuccess) {
                return Ok(result);
            }
            return BadRequest(result);
        }

        // PUT: api/Events/UpdateEvent
        [HttpPut("UpdateEvent")]
        public async Task<IActionResult> UpdateEvent([FromForm] EventUpdateDto dto) {
            if (!ModelState.IsValid) {
                return BadRequest(ModelState);
            }

            // Handle file upload
            if (dto.EventImage != null) {
                var uploadResult = await _eventService.UploadFile(dto.EventImage, "EventImages");
                Console.WriteLine($"EventsController.UpdateEvent: File uploaded, path: {uploadResult}");
                dto.EventImagePath = uploadResult;
            }

            var result = await _eventService.UpdateEvent(dto);
            if (result.IsSuccess) {
                return Ok(result);
            }
            return BadRequest(result);
        }

        // DELETE: api/Events/DeleteEvent/5
        [HttpDelete("DeleteEvent/{id}")]
        public async Task<IActionResult> DeleteEvent(int id) {
            var result = await _eventService.DeleteEvent(id);
            if (result.IsSuccess) {
                return Ok(result);
            }
            return BadRequest(result);
        }

        // POST: api/Events/ApproveEvent/5
        [HttpPost("ApproveEvent/{id}")]
        [Authorize(Roles = "Global Admin")]
        public async Task<IActionResult> ApproveEvent(int id) {
            // Get the current user's ID
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(currentUserId)) {
                return Unauthorized(new { IsSuccess = false, Message = "User not authenticated" });
            }

            var result = await _eventService.ApproveEvent(id, currentUserId);
            if (result.IsSuccess) {
                return Ok(result);
            }
            return BadRequest(result);
        }

        // POST: api/Events/RejectEvent/5
        [HttpPost("RejectEvent/{id}")]
        [Authorize(Roles = "Global Admin")]
        public async Task<IActionResult> RejectEvent(int id, [FromBody] EventRejectDto dto) {
            // Get the current user's ID
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(currentUserId)) {
                return Unauthorized(new { IsSuccess = false, Message = "User not authenticated" });
            }

            var result = await _eventService.RejectEvent(id, currentUserId, dto.RejectionReason);
            if (result.IsSuccess) {
                return Ok(result);
            }
            return BadRequest(result);
        }

        // POST: api/Events/SubmitEvent/5
        [HttpPost("SubmitEvent/{id}")]
        public async Task<IActionResult> SubmitEvent(int id) {
            var result = await _eventService.SubmitEvent(id);
            if (result.IsSuccess) {
                return Ok(result);
            }
            return BadRequest(result);
        }

        // POST: api/Events/CancelEvent/5
        [HttpPost("CancelEvent/{id}")]
        public async Task<IActionResult> CancelEvent(int id) {
            var result = await _eventService.CancelEvent(id);
            if (result.IsSuccess) {
                return Ok(result);
            }
            return BadRequest(result);
        }

        // POST: api/Events/CheckAndDeleteExpiredEvents
        [HttpPost("CheckAndDeleteExpiredEvents")]
        public async Task<IActionResult> CheckAndDeleteExpiredEvents() {
            try {
                var result = await _eventService.CheckAndDeleteExpiredEvents();
                if (result.IsSuccess) {
                    return Ok(result);
                }
                return BadRequest(result);
            }
            catch (Exception ex) {
                var apiResponse = new Server.Core.Entities.UserManagement.ResponseModel.ApiResponse<bool> {
                    IsSuccess = false,
                    Message = $"Error checking and deleting expired events: {ex.Message}",
                    Data = false
                };
                return BadRequest(apiResponse);
            }
        }
    }
}
