﻿using System.ComponentModel.DataAnnotations;

namespace Server.Core.Entities.UserManagement.RegisterModel {
    public class Register {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
        [Required]

        public string FullName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;

        public string Website { get; set; } = string.Empty;

        public string PhoneNumber { get; set; } = string.Empty;

        public string Description { get; set; } = string.Empty;

        public string Facebook { get; set; } = string.Empty;

        public string Twitter { get; set; } = string.Empty;

        public bool IsActive { get; set; }

        public List<string> Roles { get; set; } = new List<string>();
    }
}
