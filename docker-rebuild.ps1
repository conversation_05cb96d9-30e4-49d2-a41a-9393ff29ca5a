# PowerShell script to rebuild Docker containers

Write-Host "Rebuilding FullStackSFL Docker containers..." -ForegroundColor Green

# Stop and remove containers
Write-Host "Stopping and removing existing containers..." -ForegroundColor Cyan
docker-compose down

# Remove images
Write-Host "Removing existing images..." -ForegroundColor Cyan
docker rmi fullstacksfl-frontend fullstacksfl-server

# Build and start containers
Write-Host "Building and starting containers..." -ForegroundColor Cyan
docker-compose up -d --build

# Check if containers are running
$containers = docker-compose ps
if ($LASTEXITCODE -eq 0) {
    Write-Host "Containers rebuilt and started successfully!" -ForegroundColor Green
    Write-Host "You can access the application at:" -ForegroundColor Cyan
    Write-Host "  - Frontend: http://localhost:4200" -ForegroundColor White
    Write-Host "  - Backend API: http://localhost:5020" -ForegroundColor White
    Write-Host "  - Swagger UI: http://localhost:5020/swagger" -ForegroundColor White
} else {
    Write-Host "Error starting containers. Please check the logs with 'docker-compose logs'." -ForegroundColor Red
}

Write-Host "To check container status, run: .\docker-check.ps1" -ForegroundColor Cyan
