﻿﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Server.EntityFramworkCore.Data;

#nullable disable

namespace Server.EntityFramworkCore.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20240625_AddRejectionReasonToEvent")]
    partial class AddRejectionReasonToEvent
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            // Other model configurations...

            modelBuilder.Entity("Server.Core.Entities.Events.EventModel.Event", b =>
                {
                    // Existing properties...

                    b.Property<string>("RejectionReason")
                        .HasColumnType("nvarchar(max)");

                    // Other properties...
                });

            // Other entity configurations...
        }
    }
}
