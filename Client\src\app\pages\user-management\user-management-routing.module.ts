import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserListComponent } from './user-list/user-list.component';
import { AddUserComponent } from './add-user/add-user.component';
import { UserDetailsComponent } from './user-details/user-details.component';
import { RoleGuard } from '../../Core/Guards/role.guard';

const routes: Routes = [
  {
    path: '',
    component: UserListComponent,
  },
  {
    path: 'new',
    component: AddUserComponent,
    canActivate: [RoleGuard],
    data: { roles: ['Global Admin'] },
  },
  {
    path: ':id/edit',
    component: AddUserComponent,
    canActivate: [RoleGuard],
    data: { roles: ['Global Admin'] },
  },
  {
    path: ':id',
    component: UserDetailsComponent,
    canActivate: [RoleGuard],
    data: { roles: ['Global Admin'] },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UserManagementRoutingModule {}
