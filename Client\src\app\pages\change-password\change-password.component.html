<div class="container">
  <div class="header">
    <h2>Change Password</h2>
    <div class="actions">
      <button
        pButton
        class="p-button-outlined p-button-danger filter-btn"
        (click)="cancel()"
      >
        <i class="bi bi-x me-1"></i>
        Cancel
      </button>
      <button
        pButton
        class="p-button-danger filter-btn"
        (click)="onSubmit()"
        [disabled]="isLoading"
      >
        <i class="bi bi-save me-1"></i>
        Save
      </button>
    </div>
  </div>

  <div class="content">
    <form [formGroup]="passwordForm">
      <div class="form-group">
        <label for="currentPassword">Current Password</label>
        <div class="password-input-container">
          <input
            [type]="showCurrentPassword ? 'text' : 'password'"
            id="currentPassword"
            formControlName="currentPassword"
            class="form-control"
            [ngClass]="{
              'is-invalid':
                passwordForm.get('currentPassword')?.invalid &&
                passwordForm.get('currentPassword')?.touched,
            }"
          />
          <button
            type="button"
            class="toggle-password"
            (click)="togglePasswordVisibility('currentPassword')"
          >
            <i
              class="bi"
              [ngClass]="showCurrentPassword ? 'bi-eye-slash' : 'bi-eye'"
            ></i>
          </button>
        </div>
        <div
          class="invalid-feedback"
          *ngIf="
            passwordForm.get('currentPassword')?.invalid &&
            passwordForm.get('currentPassword')?.touched
          "
        >
          Current password is required
        </div>
      </div>

      <div class="form-group">
        <label for="newPassword">New Password</label>
        <div class="password-input-container">
          <input
            [type]="showNewPassword ? 'text' : 'password'"
            id="newPassword"
            formControlName="newPassword"
            class="form-control"
            [ngClass]="{
              'is-invalid':
                passwordForm.get('newPassword')?.invalid &&
                passwordForm.get('newPassword')?.touched,
            }"
          />
          <button
            type="button"
            class="toggle-password"
            (click)="togglePasswordVisibility('newPassword')"
          >
            <i
              class="bi"
              [ngClass]="showNewPassword ? 'bi-eye-slash' : 'bi-eye'"
            ></i>
          </button>
        </div>
        <div
          class="invalid-feedback"
          *ngIf="
            passwordForm.get('newPassword')?.invalid &&
            passwordForm.get('newPassword')?.touched
          "
        >
          <span *ngIf="passwordForm.get('newPassword')?.errors?.['required']"
            >New password is required</span
          >
          <span *ngIf="passwordForm.get('newPassword')?.errors?.['minlength']"
            >Password must be at least 8 characters</span
          >
          <span *ngIf="passwordForm.get('newPassword')?.errors?.['pattern']">
            Password must contain at least one uppercase letter, one lowercase
            letter, one number, and one special character
          </span>
        </div>
      </div>

      <div class="form-group">
        <label for="confirmPassword">Confirm Password</label>
        <div class="password-input-container">
          <input
            [type]="showConfirmPassword ? 'text' : 'password'"
            id="confirmPassword"
            formControlName="confirmPassword"
            class="form-control"
            [ngClass]="{
              'is-invalid':
                passwordForm.get('confirmPassword')?.invalid &&
                passwordForm.get('confirmPassword')?.touched,
            }"
          />
          <button
            type="button"
            class="toggle-password"
            (click)="togglePasswordVisibility('confirmPassword')"
          >
            <i
              class="bi"
              [ngClass]="showConfirmPassword ? 'bi-eye-slash' : 'bi-eye'"
            ></i>
          </button>
        </div>
        <div
          class="invalid-feedback"
          *ngIf="
            passwordForm.get('confirmPassword')?.invalid &&
            passwordForm.get('confirmPassword')?.touched
          "
        >
          <span
            *ngIf="passwordForm.get('confirmPassword')?.errors?.['required']"
            >Confirm password is required</span
          >
          <span
            *ngIf="passwordForm.get('confirmPassword')?.errors?.['mismatch']"
            >Passwords do not match</span
          >
        </div>
      </div>
    </form>
  </div>
</div>

<!-- Toast for notifications -->
<p-toast></p-toast>

<!-- Loading overlay -->
<div class="loading-overlay" *ngIf="isLoading">
  <div class="loading-content">
    <p-progressSpinner
      strokeWidth="4"
      [style]="{ width: '50px', height: '50px' }"
    ></p-progressSpinner>
    <div class="mt-3 text-white">Processing your request...</div>
  </div>
</div>
