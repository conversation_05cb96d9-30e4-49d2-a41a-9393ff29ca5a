using Server.Core.Entities.UserManagement.ChangePasswordModel;
using Server.Core.Entities.UserManagement.LoginModel;
using Server.Core.Entities.UserManagement.RegisterModel;
using Server.Core.Entities.UserManagement.ResponseModel;
using Server.Core.Entities.UserManagement.ToggleStatusRequestModel;
using Server.Core.Entities.UserManagement.UserDetailsModel;
using Server.Core.Entities.UserManagement.VerifyOtpRequestModel;
using Server.Core.Pagination.PagedResponseModel;
using Server.Core.Pagination.PaginationParametersModel;

namespace Server.Services.AccountServices {
    public interface IAccountService {
        Task<Response> RegisterUserAsync(Register register, string creatorId);
        Task<Response> UpdateUserAsync(string userId, UserDetails userDetails);
        Task<Response> DeleteUserAsync(string userId);
        Task<Response> LoginAsync(Login login);
        Task<Response> VerifyOtpAsync(VerifyOtpRequest request);
        Task<Response> ResendOtpAsync(string userId);
        Task<UserDetails?> GetUserByIdAsync(string userId);
        Task<List<UserDetails>> GetAllUsersAsync();
        Task<Response> ToggleUserStatusAsync(string userId, ToggleStatusRequest request, string currentUserId);
        Task<PagedResponse<UserDetails>> GetPagedUsersAsync(PaginationParameters parameters);
        Task<Response> ChangePasswordAsync(ChangePasswordRequest request);
    }
}
