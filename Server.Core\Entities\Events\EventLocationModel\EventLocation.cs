﻿﻿using Server.Core.Entities.Events.EventModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Server.Core.Entities.Events.EventLocationModel {
    public class EventLocation {

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        
        public int EventId { get; set; }
        
        // Physical location fields
        public string Address1 { get; set; }
        
        public string Address2 { get; set; }
        
        public string City { get; set; }
        
        public string State { get; set; }
        
        public string ZipCode { get; set; }
        
        public string Country { get; set; }
        
        // Online event fields
        public string MeetingId { get; set; }
        
        public string Passcode { get; set; }
        
        // Navigation property
        public virtual Event Event { get; set; }
    }
}
