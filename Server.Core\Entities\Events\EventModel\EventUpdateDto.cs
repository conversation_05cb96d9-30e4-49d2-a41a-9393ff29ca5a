﻿﻿﻿using Microsoft.AspNetCore.Http;
using Server.Core.Entities.Events.EventContactDetailsModel;
using Server.Core.Entities.Events.EventLocationModel;
using Server.Utils.ResourceUtils;
using System.ComponentModel.DataAnnotations;

namespace Server.Core.Entities.Events.EventModel {
    public class EventUpdateDto {

        [Required]
        public int Id { get; set; }

        // Basic Info
        [Required]
        public string Title { get; set; } = string.Empty;

        [Required]
        public EventType Type { get; set; }

        [Required]
        public Category Category { get; set; }

        public int? Capacity { get; set; }

        [Required]
        public string Description { get; set; } = string.Empty;

        // File upload
        [AllowedExtensions(new[] { ".jpg", ".png", ".pdf" })]
        [MaxFileSize(5 * 1024 * 1024)] // 5MB
        public IFormFile? EventImage { get; set; }

        // This will be set by the controller after saving the file
        public string? EventImagePath { get; set; }

        [Required]
        public bool RequiresRegistration { get; set; }

        // Date and Time
        [Required]
        public DateTime EventStarts { get; set; }

        [Required]
        public DateTime EventEnds { get; set; }

        public TimeSpan StartTime { get; set; }

        public TimeSpan EndTime { get; set; }

        public bool DisplayStartTime { get; set; }

        public bool DisplayEndTime { get; set; }

        // Location Type (Venue or Online)
        [Required]
        public EventLocationType LocationType { get; set; }

        // Status and Approval
        public EventStatus Status { get; set; }
        public bool IsApproved { get; set; }
        public DateTime? SubmittedOn { get; set; }
        public DateTime? ReviewedOn { get; set; }

        // Organizer Information
        public string? OrganizerId { get; set; }

        // Location Details
        public EventLocationDto Location { get; set; } = new EventLocationDto();

        // Contact Details
        public EventContactDetailsDto ContactDetails { get; set; } = new EventContactDetailsDto();
    }
}
