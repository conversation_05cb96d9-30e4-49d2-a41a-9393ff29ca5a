// Resource page container
.resource-page-container {
  min-height: 100vh;
}

// Navigation header
.resource-nav-header {
  padding: 1rem 0;
  margin-bottom: 1.5rem;

  // Using global back button styles
}

// Edit button styling
.edit-button {
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;

  i {
    margin-right: 0.5rem;
  }

  &:hover {
    background-color: #c82333;
    border-color: #bd2130;
  }
}

// Resource category badge styling
.category-badge {
  font-size: 0.9rem;
  padding: 0.4rem 0.75rem;
  border-radius: 4px;
  font-weight: 500;
  background-color: #f0f7ff;
  color: #0d6efd;
  border: none;
  display: inline-block;
  margin-bottom: 0.5rem;
  text-decoration: none;

  &:hover {
    background-color: #e1efff;
    color: #0a58ca;
  }
}

// Resource details container
.resource-details-container {
  max-width: 1200px;
  margin: 0 auto;
  padding-bottom: 3rem;
  position: relative;

  .cover-image {
    width: 100%;
    max-width: 1920px;
    margin: 0 auto;
    height: 20rem;
    overflow: hidden;
    border-radius: 0.8rem;
    background: #e3eafc;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    position: relative;
    z-index: 1;

    .resource-banner-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
      border-radius: 0.8rem;
    }
  }

  .content-container {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    margin-top: -3rem;
    max-width: 1080px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 2;
  }

  .content-section {
    background-color: #f9fafb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
  }

  // Organization logo styling
  .organization-logo {
    width: 60px;
    height: 60px;
    overflow: hidden;
    border-radius: 50%;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }
  }

  .service-bullet {
    color: #0d6efd;
    font-size: 0.5rem;
  }

  .partner-logo {
    width: 60px;
    height: 60px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .resource-details-container {
    .cover-image {
      height: 200px;
    }

    .content-container {
      padding: 1.5rem;
      margin-top: -1.5rem;
    }

    .content-section {
      padding: 1rem;
    }
  }
}
