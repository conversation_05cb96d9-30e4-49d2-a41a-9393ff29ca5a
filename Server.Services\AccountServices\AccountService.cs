using AutoMapper;
using Identity.Services.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Server.Core.Entities.UserManagement.ChangePasswordModel;
using Server.Core.Entities.UserManagement.EmailServiceModel;
using Server.Core.Entities.UserManagement.LoginModel;
using Server.Core.Entities.UserManagement.RegisterModel;
using Server.Core.Entities.UserManagement.ResponseModel;
using Server.Core.Entities.UserManagement.ToggleStatusRequestModel;
using Server.Core.Entities.UserManagement.UserDetailsModel;
using Server.Core.Entities.UserManagement.UserModel;
using Server.Core.Entities.UserManagement.VerifyOtpRequestModel;
using Server.Core.Pagination.PagedResponseModel;
using Server.Core.Pagination.PaginationParametersModel;
using Server.Services.OtpServices;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;


namespace Server.Services.AccountServices {
    public class AccountService : IAccountService {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IConfiguration _configuration;
        private readonly IEmailService _emailService;
        private readonly IOtpService _otpService;
        private readonly IMapper _mapper;

        public AccountService(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            IConfiguration configuration,
            IEmailService emailService,
            IOtpService otpService,
            IMapper mapper) {
            _userManager = userManager;
            _roleManager = roleManager;
            _configuration = configuration;
            _emailService = emailService;
            _otpService = otpService;
            _mapper = mapper;
        }

        public async Task<Response> RegisterUserAsync(Register register, string creatorId) {
            var existingUser = await _userManager.FindByEmailAsync(register.Email);
            if (existingUser != null) {
                return new Response {
                    IsSuccess = false,
                    Message = "A user with this email already exists"
                };
            }

            // Use AutoMapper to map Register to ApplicationUser
            var user = _mapper.Map<ApplicationUser>(register);
            var creatorName = _userManager.Users
                .Where(u => u.Id == creatorId)
                .Select(u => u.FullName)
                .FirstOrDefault();
            user.createdByName = creatorName;


            var result = await _userManager.CreateAsync(user, register.Password);
            if (!result.Succeeded) {
                return new Response {
                    IsSuccess = false,
                    Message = "Failed to create user",
                    Data = result.Errors
                };
            }

            if (register.Roles != null && register.Roles.Any()) {
                foreach (var role in register.Roles) {
                    if (!await _roleManager.RoleExistsAsync(role)) {
                        await _roleManager.CreateAsync(new IdentityRole(role));
                    }
                }
                await _userManager.AddToRolesAsync(user, register.Roles);
            }

            return new Response {
                IsSuccess = true,
                Message = "User created successfully"
            };
        }

        public async Task<Response> UpdateUserAsync(string userId, UserDetails userDetails) {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found"
                };
            }

            user.FullName = userDetails.FullName ?? user.FullName;
            user.PhoneNumber = userDetails.PhoneNumber ?? user.PhoneNumber;
            user.Website = userDetails.Website ?? user.Website;
            user.Description = userDetails.Description ?? user.Description;
            user.Facebook = userDetails.Facebook ?? user.Facebook;
            user.Twitter = userDetails.Twitter ?? user.Twitter;

            var result = await _userManager.UpdateAsync(user);
            if (!result.Succeeded) {
                return new Response {
                    IsSuccess = false,
                    Message = "Failed to update user",
                    Data = result.Errors
                };
            }

            if (userDetails.Roles != null && userDetails.Roles.Any()) {
                var currentRoles = await _userManager.GetRolesAsync(user);
                var rolesToAdd = userDetails.Roles.Except(currentRoles).ToList();
                var rolesToRemove = currentRoles.Except(userDetails.Roles).ToList();

                if (rolesToAdd.Any()) {
                    await _userManager.AddToRolesAsync(user, rolesToAdd);
                }

                if (rolesToRemove.Any()) {
                    await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
                }
            }

            return new Response {
                IsSuccess = true,
                Message = "User updated successfully"
            };
        }

        public async Task<Response> DeleteUserAsync(string userId) {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found"
                };
            }

            var result = await _userManager.DeleteAsync(user);
            if (!result.Succeeded) {
                return new Response {
                    IsSuccess = false,
                    Message = "Failed to delete user",
                    Data = result.Errors
                };
            }

            return new Response {
                IsSuccess = true,
                Message = "User deleted successfully"
            };
        }

        public async Task<Response> LoginAsync(Login login) {
            var user = await _userManager.FindByEmailAsync(login.Email);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found with this email"
                };
            }

            var isPasswordValid = await _userManager.CheckPasswordAsync(user, login.Password);
            if (!isPasswordValid) {
                return new Response {
                    IsSuccess = false,
                    Message = "Invalid password"
                };
            }

            // Check if user is active
            if (!user.IsActive) {
                return new Response {
                    IsSuccess = false,
                    Message = "Your account is inactive. Please contact an administrator to activate your account."
                };
            }

            var recentlyVerified = await _otpService.IsOtpRecentlyVerified(user.Id);
            if (!recentlyVerified) {
                var otp = GenerateOtp();
                var message = new Message(
                    new[] { user.Email ?? string.Empty },
                    "Your OTP Code",
                    $"Your OTP code is: {otp}. It is valid for 5 min."
                );
                _emailService.SendEmail(message);
                await _otpService.StoreOtp(user.Id, otp);

                return new Response {
                    IsSuccess = true,
                    Message = "OTP sent to your email. Please verify.",
                    Data = new { RequiresOtp = true, UserId = user.Id }
                };
            }

            var token = GenerateToken(user);
            return new Response {
                IsSuccess = true,
                Message = "Login successful",
                Token = token,
                Data = new { RequiresOtp = false, UserId = user.Id }
            };
        }

        public async Task<Response> VerifyOtpAsync(VerifyOtpRequest request) {
            var user = await _userManager.FindByIdAsync(request.UserId);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found"
                };
            }

            // Check if user is active
            if (!user.IsActive) {
                return new Response {
                    IsSuccess = false,
                    Message = "Your account is inactive. Please contact an administrator to activate your account."
                };
            }

            var isValid = await _otpService.VerifyOtp(user.Id, request.Otp);
            if (!isValid) {
                return new Response {
                    IsSuccess = false,
                    Message = "Invalid or expired OTP"
                };
            }

            await _otpService.MarkOtpVerified(user.Id);
            var token = GenerateToken(user);

            return new Response {
                IsSuccess = true,
                Message = "OTP verified, login successful",
                Token = token
            };
        }

        public async Task<UserDetails?> GetUserByIdAsync(string userId) {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) return null;

            var roles = await _userManager.GetRolesAsync(user);


            return new UserDetails {
                Id = user.Id,
                FullName = user.FullName,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber,
                Website = user.Website,
                Description = user.Description,
                Facebook = user.Facebook,
                Twitter = user.Twitter,
                IsActive = user.IsActive,
                CreatedOn = user.CreatedOn,
                CreatedByName = user.createdByName,
                Roles = roles.ToList()
            };
        }

        public async Task<List<UserDetails>> GetAllUsersAsync() {
            var users = _userManager.Users.ToList();
            var result = new List<UserDetails>();

            foreach (var user in users) {
                var roles = await _userManager.GetRolesAsync(user);


                result.Add(new UserDetails {
                    Id = user.Id,
                    FullName = user.FullName,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    Website = user.Website,
                    Description = user.Description,
                    Facebook = user.Facebook,
                    Twitter = user.Twitter,
                    IsActive = user.IsActive,
                    CreatedOn = user.CreatedOn,
                    CreatedByName = user.createdByName,
                    Roles = roles.ToList()
                });
            }

            return result;
        }
        public async Task<Response> ToggleUserStatusAsync(string userId, ToggleStatusRequest request, string currentUserId) {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found"
                };
            }

            // Check if user is trying to deactivate themselves
            if (userId == currentUserId && !request.IsActive) {
                return new Response {
                    IsSuccess = false,
                    Message = "You cannot deactivate your own account"
                };
            }

            user.IsActive = request.IsActive;
            var result = await _userManager.UpdateAsync(user);

            if (result.Succeeded) {
                return new Response {
                    IsSuccess = true,
                    Message = "User status updated successfully"
                };
            }

            return new Response {
                IsSuccess = false,
                Message = "Failed to update user status"
            };
        }

        public async Task<PagedResponse<UserDetails>> GetPagedUsersAsync(PaginationParameters parameters) {
            try {
                // Start with the base query
                var query = _userManager.Users.AsQueryable();

                // Apply non-role filters first
                if (!string.IsNullOrWhiteSpace(parameters.Name))
                    query = query.Where(u => u.FullName.Contains(parameters.Name));

                if (!string.IsNullOrWhiteSpace(parameters.createdByName))
                    query = query.Where(u => u.createdByName.Contains(parameters.createdByName));

                if (!string.IsNullOrWhiteSpace(parameters.Status)) {
                    var isActive = parameters.Status.ToLower() == "active";
                    query = query.Where(u => u.IsActive == isActive);
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(parameters.SortField)) {
                    var isAscending = parameters.SortOrder?.ToLower() != "desc";
                    query = ApplySorting(query, parameters.SortField, isAscending);
                }
                else {
                    query = query.OrderByDescending(u => u.CreatedOn);
                }

                // Execute the query to get the initial list of users
                var users = await query.ToListAsync();

                // Filter by role if specified (in memory)
                if (!string.IsNullOrWhiteSpace(parameters.Role)) {
                    var filteredUsers = new List<ApplicationUser>();
                    foreach (var user in users) {
                        var userRoles = await _userManager.GetRolesAsync(user);
                        if (userRoles.Any(r => r.Equals(parameters.Role, StringComparison.OrdinalIgnoreCase))) {
                            filteredUsers.Add(user);
                        }
                    }
                    users = filteredUsers;
                }

                // Calculate total items after role filtering
                var totalItems = users.Count;

                // Apply pagination after role filtering
                var pagedUsers = users
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                // Map to UserDetails
                var userDetailsList = new List<UserDetails>();
                foreach (var user in pagedUsers) {
                    var roles = await _userManager.GetRolesAsync(user);
                    userDetailsList.Add(new UserDetails {
                        Id = user.Id,
                        FullName = user.FullName,
                        Email = user.Email,
                        PhoneNumber = user.PhoneNumber,
                        Website = user.Website,
                        Description = user.Description,
                        Facebook = user.Facebook,
                        Twitter = user.Twitter,
                        IsActive = user.IsActive,
                        CreatedOn = user.CreatedOn,
                        CreatedByName = user.createdByName ?? string.Empty,
                        Roles = roles.ToList()
                    });
                }

                return new PagedResponse<UserDetails>(userDetailsList, totalItems, parameters.PageNumber, parameters.PageSize);
            }
            catch (Exception ex) {
                // Log the exception details here if you have logging configured
                throw; // Re-throw the exception to be handled by the controller
            }
        }

        private IQueryable<ApplicationUser> ApplySorting(IQueryable<ApplicationUser> query, string sortField, bool isAscending) {
            return sortField.ToLower() switch {
                "fullname" => isAscending ? query.OrderBy(u => u.FullName) : query.OrderByDescending(u => u.FullName),
                "email" => isAscending ? query.OrderBy(u => u.Email) : query.OrderByDescending(u => u.Email),
                "createdon" => isAscending ? query.OrderBy(u => u.CreatedOn) : query.OrderByDescending(u => u.CreatedOn),
                "isactive" => isAscending ? query.OrderBy(u => u.IsActive) : query.OrderByDescending(u => u.IsActive),
                _ => query.OrderByDescending(u => u.CreatedOn)
            };
        }

        private string GenerateToken(ApplicationUser user) {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_configuration["JWTSetting:securityKey"]);

            var roles = _userManager.GetRolesAsync(user).Result;
            var claims = new List<Claim>
            {
                new(JwtRegisteredClaimNames.Email, user.Email ?? ""),
                new(JwtRegisteredClaimNames.Name, user.FullName ?? ""),
                new(JwtRegisteredClaimNames.NameId, user.Id ?? ""),
                new(JwtRegisteredClaimNames.Aud, _configuration["JWTSetting:ValidAudience"]),
                new(JwtRegisteredClaimNames.Iss, _configuration["JWTSetting:ValidIssuer"])
            };

            claims.AddRange(roles.Select(role => new Claim(ClaimTypes.Role, role)));

            var tokenDescriptor = new SecurityTokenDescriptor {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(4),
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(key),
                    SecurityAlgorithms.HmacSha256
                )
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        private string GenerateOtp() {
            var random = new Random();
            return random.Next(10000, 99999).ToString();
        }

        public async Task<Response> ChangePasswordAsync(ChangePasswordRequest request) {
            var user = await _userManager.FindByIdAsync(request.UserId);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found"
                };
            }

            // Verify the current password
            var isCurrentPasswordValid = await _userManager.CheckPasswordAsync(user, request.CurrentPassword);
            if (!isCurrentPasswordValid) {
                return new Response {
                    IsSuccess = false,
                    Message = "Current password is incorrect"
                };
            }

            // Change the password
            var result = await _userManager.ChangePasswordAsync(user, request.CurrentPassword, request.NewPassword);
            if (!result.Succeeded) {
                return new Response {
                    IsSuccess = false,
                    Message = "Failed to change password",
                    Data = result.Errors
                };
            }

            return new Response {
                IsSuccess = true,
                Message = "Password changed successfully"
            };
        }

        public async Task<Response> ResendOtpAsync(string userId) {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null) {
                return new Response {
                    IsSuccess = false,
                    Message = "User not found"
                };
            }

            // Check if user is active
            if (!user.IsActive) {
                return new Response {
                    IsSuccess = false,
                    Message = "Your account is inactive. Please contact an administrator to activate your account."
                };
            }

            // Generate a new OTP
            var otp = GenerateOtp();

            // Store the OTP
            await _otpService.StoreOtp(user.Id, otp);

            // Send the OTP via email
            var message = new Message(
                new[] { user.Email ?? string.Empty },
                "Your New OTP Code",
                $"Your new OTP code is: {otp}. It is valid for 5 min."
            );
            _emailService.SendEmail(message);

            return new Response {
                IsSuccess = true,
                Message = "New OTP sent to your email. Please verify.",
                Data = new { UserId = user.Id }
            };
        }
    }
}
